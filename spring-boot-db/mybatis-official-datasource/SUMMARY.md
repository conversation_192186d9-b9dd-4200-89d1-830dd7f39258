# MyBatis官方推荐多数据源项目总结

## 项目概述

`mybatis-official-datasource` 是一个严格按照Spring Boot官方文档推荐方式实现的MyBatis多数据源示例项目。该项目采用`DataSourceProperties`和`@ConfigurationProperties`实现类型安全的配置绑定，支持HikariCP连接池的完整配置选项。

## 核心特性

### 1. 官方推荐配置方式

**主数据源配置**:
```java
@Primary
@Bean(name = "primaryDataSourceProperties")
@ConfigurationProperties("spring.datasource")
public DataSourceProperties primaryDataSourceProperties() {
    return new DataSourceProperties();
}

@Primary
@Bean(name = "primaryDataSource")
@ConfigurationProperties("spring.datasource.hikari")
public HikariDataSource primaryDataSource(DataSourceProperties properties) {
    return properties.initializeDataSourceBuilder()
            .type(HikariDataSource.class)
            .build();
}
```

**附加数据源配置**:
```java
@Qualifier("second")
@Bean(name = "secondaryDataSource", defaultCandidate = false)
@ConfigurationProperties("app.datasource.configuration")
public HikariDataSource secondaryDataSource(DataSourceProperties properties) {
    return properties.initializeDataSourceBuilder()
            .type(HikariDataSource.class)
            .build();
}
```

### 2. 类型安全的配置绑定
- 使用`DataSourceProperties`确保配置的类型安全
- 支持IDE的自动补全和配置验证
- 避免配置错误导致的运行时异常

### 3. 完整的HikariCP配置支持
```yaml
app:
  datasource:
    configuration:
      pool-name: SecondaryHikariPool
      maximum-pool-size: 15
      minimum-idle: 3
      connection-timeout: 30000
      leak-detection-threshold: 60000
      connection-init-sql: SELECT 1
      validation-timeout: 5000
```

### 4. 企业级架构设计
- 三个独立数据源：账户、交易、审计日志
- 清晰的模块分离和包结构
- 完整的XML映射文件配置
- 支持复杂SQL和动态SQL

## 技术架构

```
┌─────────────────────────────────────────────────────────┐
│                   Application Layer                     │
├─────────────────────────────────────────────────────────┤
│  Controller  │  Controller  │  Controller              │
│  (Account)   │ (Transaction)│ (AuditLog)               │
├─────────────────────────────────────────────────────────┤
│   Service    │   Service    │   Service                │
│  (Account)   │ (Transaction)│ (AuditLog)               │
├─────────────────────────────────────────────────────────┤
│   Mapper     │   Mapper     │   Mapper                 │
│  (Account)   │ (Transaction)│ (AuditLog)               │
├─────────────────────────────────────────────────────────┤
│ SqlSession   │ SqlSession   │ SqlSession               │
│ (Primary)    │ (Secondary)  │ (Third)                  │
├─────────────────────────────────────────────────────────┤
│ DataSource   │ DataSource   │ DataSource               │
│ (Primary)    │ (Secondary)  │ (Third)                  │
├─────────────────────────────────────────────────────────┤
│ primary_db   │ secondary_db │ third_db                 │
│ (Accounts)   │(Transactions)│ (AuditLogs)              │
└─────────────────────────────────────────────────────────┘
```

## 配置对比

| 配置方式 | 类型安全 | 配置完整性 | IDE支持 | 维护性 |
|----------|----------|------------|---------|--------|
| 手动配置 | ❌ | ⭐⭐ | ❌ | ⭐⭐ |
| 基础@ConfigurationProperties | ⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| **官方推荐DataSourceProperties** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

## 项目结构

```
mybatis-official-datasource/
├── 配置层
│   └── DataSourceConfiguration.java    # 官方推荐配置
├── 业务模块
│   ├── primary/                        # 账户管理模块
│   ├── secondary/                      # 交易管理模块
│   └── third/                          # 审计日志模块
├── 映射文件
│   ├── mapper/primary/                 # 账户映射
│   ├── mapper/secondary/               # 交易映射
│   └── mapper/third/                   # 审计映射
└── 数据库脚本
    └── sql/                           # 初始化脚本
```

## 使用场景

### 1. 企业级应用
- 需要多个业务数据库的大型应用
- 对配置质量和类型安全有严格要求
- 需要精细化连接池配置和性能调优

### 2. 金融系统
- 账户管理、交易记录、审计日志分离
- 严格的数据隔离和安全要求
- 高并发和高可用性需求

### 3. 微服务架构
- 每个服务管理自己的数据库
- 需要统一的配置管理方式
- 支持服务间的数据隔离

## 优势特点

### 1. 配置优势
- **类型安全**: DataSourceProperties确保配置正确性
- **IDE友好**: 完整的自动补全和配置验证
- **官方推荐**: 遵循Spring Boot最佳实践

### 2. 功能优势
- **配置完整**: 支持HikariCP所有配置选项
- **性能优化**: 连接泄漏检测、连接验证等高级特性
- **监控支持**: 集成Spring Boot Actuator

### 3. 架构优势
- **模块清晰**: 每个数据源独立配置和管理
- **扩展性强**: 易于添加新的数据源
- **维护性好**: 清晰的配置结构和命名规范

## 配置示例

### 完整的HikariCP配置
```yaml
app:
  datasource:
    url: ****************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    configuration:
      # 连接池配置
      pool-name: SecondaryHikariPool
      maximum-pool-size: 15
      minimum-idle: 3
      
      # 超时配置
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      validation-timeout: 5000
      
      # 高级配置
      leak-detection-threshold: 60000
      connection-init-sql: SELECT 1
      connection-test-query: SELECT 1
      allow-pool-suspension: false
      
      # 性能优化
      keepalive-time: 0
      register-mbeans: true
```

## 最佳实践

### 1. 配置管理
- 使用环境变量管理敏感信息
- 为不同环境提供不同的配置文件
- 启用配置加密和安全存储

### 2. 连接池优化
- 根据实际负载调整连接池大小
- 启用连接泄漏检测
- 配置合适的超时时间

### 3. 监控和运维
- 启用JMX监控
- 配置健康检查端点
- 设置连接池指标监控

## 快速开始

```bash
# 1. 创建数据库
mysql -u root -p -e "CREATE DATABASE primary_db;"
mysql -u root -p -e "CREATE DATABASE secondary_db;"
mysql -u root -p -e "CREATE DATABASE third_db;"

# 2. 执行初始化脚本
mysql -u root -p < src/main/resources/sql/primary_db_schema.sql

# 3. 启动应用
mvn spring-boot:run

# 4. 访问应用
curl http://localhost:8086/actuator/health
```

## 总结

`mybatis-official-datasource` 项目展示了Spring Boot官方推荐的多数据源配置方式，具有以下特点：

- ✅ **官方认可**: 完全遵循Spring Boot官方文档
- ✅ **类型安全**: 使用DataSourceProperties确保配置正确
- ✅ **功能完整**: 支持HikariCP的所有高级特性
- ✅ **企业级**: 适合大型企业应用的架构设计
- ✅ **可维护**: 清晰的配置结构和最佳实践

这个项目是学习Spring Boot多数据源配置的最佳参考，特别适合对配置质量和类型安全有较高要求的企业级应用开发。
