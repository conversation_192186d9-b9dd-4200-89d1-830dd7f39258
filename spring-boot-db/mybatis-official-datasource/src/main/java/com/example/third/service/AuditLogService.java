package com.example.third.service;

import com.example.third.domain.AuditLog;
import com.example.third.mapper.AuditLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志服务类 - 第三数据源
 * 提供审计日志相关的业务逻辑处理
 */
@Service
@Transactional(transactionManager = "thirdTransactionManager")
public class AuditLogService {

    @Autowired
    private AuditLogMapper auditLogMapper;

    /**
     * 根据ID查询审计日志
     */
    public AuditLog getById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("日志ID不能为空");
        }
        return auditLogMapper.selectById(id);
    }

    /**
     * 查询所有审计日志
     */
    public List<AuditLog> getAllLogs() {
        return auditLogMapper.selectAll();
    }

    /**
     * 根据操作类型查询审计日志
     */
    public List<AuditLog> getByOperationType(String operationType) {
        if (operationType == null || operationType.trim().isEmpty()) {
            throw new IllegalArgumentException("操作类型不能为空");
        }
        return auditLogMapper.selectByOperationType(operationType);
    }

    /**
     * 根据操作对象查询审计日志
     */
    public List<AuditLog> getByOperationTarget(String operationTarget) {
        if (operationTarget == null || operationTarget.trim().isEmpty()) {
            throw new IllegalArgumentException("操作对象不能为空");
        }
        return auditLogMapper.selectByOperationTarget(operationTarget);
    }

    /**
     * 根据用户ID查询审计日志
     */
    public List<AuditLog> getByUserId(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return auditLogMapper.selectByUserId(userId);
    }

    /**
     * 根据用户名查询审计日志
     */
    public List<AuditLog> getByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        return auditLogMapper.selectByUsername(username);
    }

    /**
     * 根据操作结果查询审计日志
     */
    public List<AuditLog> getByResult(String result) {
        if (result == null || result.trim().isEmpty()) {
            throw new IllegalArgumentException("操作结果不能为空");
        }
        return auditLogMapper.selectByResult(result);
    }

    /**
     * 根据时间范围查询审计日志
     */
    public List<AuditLog> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        return auditLogMapper.selectByTimeRange(startTime, endTime);
    }

    /**
     * 根据IP地址查询审计日志
     */
    public List<AuditLog> getByIpAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            throw new IllegalArgumentException("IP地址不能为空");
        }
        return auditLogMapper.selectByIpAddress(ipAddress);
    }

    /**
     * 分页查询审计日志
     */
    public List<AuditLog> getByPage(Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        Integer offset = (page - 1) * size;
        return auditLogMapper.selectByPage(offset, size);
    }

    /**
     * 统计审计日志总数
     */
    public int getTotalCount() {
        return auditLogMapper.countAll();
    }

    /**
     * 根据操作类型统计日志数
     */
    public int getCountByOperationType(String operationType) {
        if (operationType == null || operationType.trim().isEmpty()) {
            throw new IllegalArgumentException("操作类型不能为空");
        }
        return auditLogMapper.countByOperationType(operationType);
    }

    /**
     * 根据操作结果统计日志数
     */
    public int getCountByResult(String result) {
        if (result == null || result.trim().isEmpty()) {
            throw new IllegalArgumentException("操作结果不能为空");
        }
        return auditLogMapper.countByResult(result);
    }

    /**
     * 根据用户ID统计日志数
     */
    public int getCountByUserId(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return auditLogMapper.countByUserId(userId);
    }

    /**
     * 创建审计日志
     */
    public AuditLog createLog(AuditLog auditLog) {
        if (auditLog == null) {
            throw new IllegalArgumentException("审计日志信息不能为空");
        }
        
        // 验证必填字段
        validateAuditLog(auditLog);
        
        // 设置默认值
        if (auditLog.getResult() == null) {
            auditLog.setResult("SUCCESS");
        }
        if (auditLog.getOperationTime() == null) {
            auditLog.setOperationTime(LocalDateTime.now());
        }
        
        auditLog.setCreateTime(LocalDateTime.now());
        
        auditLogMapper.insert(auditLog);
        return auditLog;
    }

    /**
     * 记录操作日志 - 便捷方法
     */
    public void logOperation(String operationType, String operationTarget, Long targetId, 
                           Long userId, String username, String ipAddress, String details) {
        AuditLog auditLog = new AuditLog();
        auditLog.setOperationType(operationType);
        auditLog.setOperationTarget(operationTarget);
        auditLog.setTargetId(targetId);
        auditLog.setUserId(userId);
        auditLog.setUsername(username);
        auditLog.setIpAddress(ipAddress);
        auditLog.setOperationDetails(details);
        auditLog.setResult("SUCCESS");
        auditLog.setOperationTime(LocalDateTime.now());
        auditLog.setCreateTime(LocalDateTime.now());
        
        auditLogMapper.insertSelective(auditLog);
    }

    /**
     * 记录失败操作日志 - 便捷方法
     */
    public void logFailedOperation(String operationType, String operationTarget, Long targetId, 
                                 Long userId, String username, String ipAddress, String details, 
                                 String errorMessage) {
        AuditLog auditLog = new AuditLog();
        auditLog.setOperationType(operationType);
        auditLog.setOperationTarget(operationTarget);
        auditLog.setTargetId(targetId);
        auditLog.setUserId(userId);
        auditLog.setUsername(username);
        auditLog.setIpAddress(ipAddress);
        auditLog.setOperationDetails(details);
        auditLog.setResult("FAILURE");
        auditLog.setErrorMessage(errorMessage);
        auditLog.setOperationTime(LocalDateTime.now());
        auditLog.setCreateTime(LocalDateTime.now());
        
        auditLogMapper.insertSelective(auditLog);
    }

    /**
     * 更新审计日志
     */
    public AuditLog updateLog(AuditLog auditLog) {
        if (auditLog == null || auditLog.getId() == null) {
            throw new IllegalArgumentException("日志ID不能为空");
        }
        
        // 检查日志是否存在
        AuditLog existingLog = auditLogMapper.selectById(auditLog.getId());
        if (existingLog == null) {
            throw new IllegalArgumentException("审计日志不存在：" + auditLog.getId());
        }
        
        auditLogMapper.updateByIdSelective(auditLog);
        
        return auditLogMapper.selectById(auditLog.getId());
    }

    /**
     * 删除审计日志
     */
    public void deleteLog(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("日志ID不能为空");
        }
        
        // 检查日志是否存在
        AuditLog auditLog = auditLogMapper.selectById(id);
        if (auditLog == null) {
            throw new IllegalArgumentException("审计日志不存在：" + id);
        }
        
        auditLogMapper.deleteById(id);
    }

    /**
     * 批量创建审计日志
     */
    public void batchCreateLogs(List<AuditLog> auditLogs) {
        if (auditLogs == null || auditLogs.isEmpty()) {
            throw new IllegalArgumentException("审计日志列表不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        for (AuditLog auditLog : auditLogs) {
            validateAuditLog(auditLog);
            
            // 设置默认值
            if (auditLog.getResult() == null) {
                auditLog.setResult("SUCCESS");
            }
            if (auditLog.getOperationTime() == null) {
                auditLog.setOperationTime(now);
            }
            
            auditLog.setCreateTime(now);
        }
        
        auditLogMapper.batchInsert(auditLogs);
    }

    /**
     * 批量删除审计日志
     */
    public void batchDeleteLogs(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("日志ID列表不能为空");
        }
        
        // 检查所有日志是否存在
        for (Long id : ids) {
            AuditLog auditLog = auditLogMapper.selectById(id);
            if (auditLog == null) {
                throw new IllegalArgumentException("审计日志不存在：" + id);
            }
        }
        
        auditLogMapper.batchDelete(ids);
    }

    /**
     * 删除指定时间之前的日志
     */
    public int deleteLogsBefore(LocalDateTime time) {
        if (time == null) {
            throw new IllegalArgumentException("时间不能为空");
        }
        return auditLogMapper.deleteByTimeBefore(time);
    }

    /**
     * 获取今日操作统计
     */
    public List<AuditLog> getTodayLogs() {
        return auditLogMapper.getTodayLogs();
    }

    /**
     * 获取失败操作日志
     */
    public List<AuditLog> getFailedOperations() {
        return auditLogMapper.getFailedOperations();
    }

    /**
     * 获取用户操作统计
     */
    public List<AuditLog> getUserOperationStats(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        return auditLogMapper.getUserOperationStats(userId, startTime, endTime);
    }

    /**
     * 获取操作类型统计
     */
    public List<AuditLog> getOperationTypeStats(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        return auditLogMapper.getOperationTypeStats(startTime, endTime);
    }

    /**
     * 验证审计日志信息
     */
    private void validateAuditLog(AuditLog auditLog) {
        if (auditLog.getOperationType() == null || auditLog.getOperationType().trim().isEmpty()) {
            throw new IllegalArgumentException("操作类型不能为空");
        }
        if (auditLog.getOperationTarget() == null || auditLog.getOperationTarget().trim().isEmpty()) {
            throw new IllegalArgumentException("操作对象不能为空");
        }
        if (auditLog.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (auditLog.getUsername() == null || auditLog.getUsername().trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
    }
}
