package com.example.third.domain;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 审计日志实体类 - 第三数据源
 * 用于演示官方推荐的多数据源配置
 */
public class AuditLog {
    
    private Long id;
    
    @NotBlank(message = "操作类型不能为空")
    private String operationType;
    
    @NotBlank(message = "操作对象不能为空")
    private String operationTarget;
    
    private Long targetId;
    
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    private String username;
    
    private String ipAddress;
    
    private String userAgent;
    
    private String operationDetails;
    
    private String result;
    
    private String errorMessage;
    
    private Long executionTime;
    
    private LocalDateTime operationTime;
    
    private LocalDateTime createTime;

    public AuditLog() {
    }

    public AuditLog(String operationType, String operationTarget, Long targetId, 
                   Long userId, String username) {
        this.operationType = operationType;
        this.operationTarget = operationTarget;
        this.targetId = targetId;
        this.userId = userId;
        this.username = username;
        this.result = "SUCCESS";
        this.operationTime = LocalDateTime.now();
        this.createTime = LocalDateTime.now();
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationTarget() {
        return operationTarget;
    }

    public void setOperationTarget(String operationTarget) {
        this.operationTarget = operationTarget;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getOperationDetails() {
        return operationDetails;
    }

    public void setOperationDetails(String operationDetails) {
        this.operationDetails = operationDetails;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "AuditLog{" +
                "id=" + id +
                ", operationType='" + operationType + '\'' +
                ", operationTarget='" + operationTarget + '\'' +
                ", targetId=" + targetId +
                ", userId=" + userId +
                ", username='" + username + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", operationDetails='" + operationDetails + '\'' +
                ", result='" + result + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", executionTime=" + executionTime +
                ", operationTime=" + operationTime +
                ", createTime=" + createTime +
                '}';
    }
}
