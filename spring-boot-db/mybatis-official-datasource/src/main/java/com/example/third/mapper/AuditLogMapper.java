package com.example.third.mapper;

import com.example.third.domain.AuditLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志Mapper接口 - 第三数据源
 * 使用XML映射文件配置SQL
 */
@Repository
public interface AuditLogMapper {

    /**
     * 根据ID查询审计日志
     */
    AuditLog selectById(Long id);

    /**
     * 查询所有审计日志
     */
    List<AuditLog> selectAll();

    /**
     * 根据操作类型查询审计日志
     */
    List<AuditLog> selectByOperationType(String operationType);

    /**
     * 根据操作对象查询审计日志
     */
    List<AuditLog> selectByOperationTarget(String operationTarget);

    /**
     * 根据用户ID查询审计日志
     */
    List<AuditLog> selectByUserId(Long userId);

    /**
     * 根据用户名查询审计日志
     */
    List<AuditLog> selectByUsername(String username);

    /**
     * 根据操作结果查询审计日志
     */
    List<AuditLog> selectByResult(String result);

    /**
     * 根据时间范围查询审计日志
     */
    List<AuditLog> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 根据IP地址查询审计日志
     */
    List<AuditLog> selectByIpAddress(String ipAddress);

    /**
     * 分页查询审计日志
     */
    List<AuditLog> selectByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 统计审计日志总数
     */
    int countAll();

    /**
     * 根据操作类型统计日志数
     */
    int countByOperationType(String operationType);

    /**
     * 根据操作结果统计日志数
     */
    int countByResult(String result);

    /**
     * 根据用户ID统计日志数
     */
    int countByUserId(Long userId);

    /**
     * 插入审计日志
     */
    int insert(AuditLog auditLog);

    /**
     * 选择性插入审计日志
     */
    int insertSelective(AuditLog auditLog);

    /**
     * 根据ID更新审计日志
     */
    int updateById(AuditLog auditLog);

    /**
     * 选择性更新审计日志
     */
    int updateByIdSelective(AuditLog auditLog);

    /**
     * 根据ID删除审计日志
     */
    int deleteById(Long id);

    /**
     * 批量插入审计日志
     */
    int batchInsert(List<AuditLog> auditLogs);

    /**
     * 批量删除审计日志
     */
    int batchDelete(List<Long> ids);

    /**
     * 删除指定时间之前的日志
     */
    int deleteByTimeBefore(LocalDateTime time);

    /**
     * 获取今日操作统计
     */
    List<AuditLog> getTodayLogs();

    /**
     * 获取失败操作日志
     */
    List<AuditLog> getFailedOperations();

    /**
     * 获取用户操作统计
     */
    List<AuditLog> getUserOperationStats(@Param("userId") Long userId, 
                                        @Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取操作类型统计
     */
    List<AuditLog> getOperationTypeStats(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);
}
