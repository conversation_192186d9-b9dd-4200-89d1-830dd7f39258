package com.example.third.controller;

import com.example.third.domain.AuditLog;
import com.example.third.service.AuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审计日志控制器 - 第三数据源
 * 提供审计日志相关的RESTful API
 */
@RestController
@RequestMapping("/api/audit-logs")
@CrossOrigin(origins = "*")
public class AuditLogController {

    @Autowired
    private AuditLogService auditLogService;

    /**
     * 根据ID查询审计日志
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            AuditLog auditLog = auditLogService.getById(id);
            if (auditLog != null) {
                response.put("success", true);
                response.put("data", auditLog);
                response.put("message", "查询成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "审计日志不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 查询审计日志列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getLogs(
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) String operationTarget,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String result,
            @RequestParam(required = false) String ipAddress,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        Map<String, Object> response = new HashMap<>();
        try {
            List<AuditLog> logs;
            
            if (operationType != null) {
                logs = auditLogService.getByOperationType(operationType);
            } else if (operationTarget != null) {
                logs = auditLogService.getByOperationTarget(operationTarget);
            } else if (userId != null) {
                logs = auditLogService.getByUserId(userId);
            } else if (username != null) {
                logs = auditLogService.getByUsername(username);
            } else if (result != null) {
                logs = auditLogService.getByResult(result);
            } else if (ipAddress != null) {
                logs = auditLogService.getByIpAddress(ipAddress);
            } else if (startTime != null || endTime != null) {
                logs = auditLogService.getByTimeRange(startTime, endTime);
            } else if (page != null && size != null) {
                logs = auditLogService.getByPage(page, size);
            } else {
                logs = auditLogService.getAllLogs();
            }
            
            response.put("success", true);
            response.put("data", logs);
            response.put("total", auditLogService.getTotalCount());
            response.put("message", "查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 创建审计日志
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createLog(@Valid @RequestBody AuditLog auditLog) {
        Map<String, Object> response = new HashMap<>();
        try {
            AuditLog createdLog = auditLogService.createLog(auditLog);
            response.put("success", true);
            response.put("data", createdLog);
            response.put("message", "审计日志创建成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "审计日志创建失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 记录操作日志 - 便捷接口
     */
    @PostMapping("/log-operation")
    public ResponseEntity<Map<String, Object>> logOperation(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        try {
            String operationType = (String) request.get("operationType");
            String operationTarget = (String) request.get("operationTarget");
            Long targetId = request.get("targetId") != null ? Long.valueOf(request.get("targetId").toString()) : null;
            Long userId = Long.valueOf(request.get("userId").toString());
            String username = (String) request.get("username");
            String ipAddress = (String) request.get("ipAddress");
            String details = (String) request.get("details");
            
            auditLogService.logOperation(operationType, operationTarget, targetId, userId, username, ipAddress, details);
            
            response.put("success", true);
            response.put("message", "操作日志记录成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "操作日志记录失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 记录失败操作日志 - 便捷接口
     */
    @PostMapping("/log-failed-operation")
    public ResponseEntity<Map<String, Object>> logFailedOperation(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        try {
            String operationType = (String) request.get("operationType");
            String operationTarget = (String) request.get("operationTarget");
            Long targetId = request.get("targetId") != null ? Long.valueOf(request.get("targetId").toString()) : null;
            Long userId = Long.valueOf(request.get("userId").toString());
            String username = (String) request.get("username");
            String ipAddress = (String) request.get("ipAddress");
            String details = (String) request.get("details");
            String errorMessage = (String) request.get("errorMessage");
            
            auditLogService.logFailedOperation(operationType, operationTarget, targetId, userId, username, ipAddress, details, errorMessage);
            
            response.put("success", true);
            response.put("message", "失败操作日志记录成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "失败操作日志记录失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 更新审计日志
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateLog(@PathVariable Long id, 
                                                        @Valid @RequestBody AuditLog auditLog) {
        Map<String, Object> response = new HashMap<>();
        try {
            auditLog.setId(id);
            AuditLog updatedLog = auditLogService.updateLog(auditLog);
            response.put("success", true);
            response.put("data", updatedLog);
            response.put("message", "审计日志更新成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "审计日志更新失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除审计日志
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteLog(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            auditLogService.deleteLog(id);
            response.put("success", true);
            response.put("message", "审计日志删除成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "审计日志删除失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量创建审计日志
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchCreateLogs(@Valid @RequestBody List<AuditLog> auditLogs) {
        Map<String, Object> response = new HashMap<>();
        try {
            auditLogService.batchCreateLogs(auditLogs);
            response.put("success", true);
            response.put("message", "批量创建审计日志成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量创建审计日志失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量删除审计日志
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteLogs(@RequestBody List<Long> ids) {
        Map<String, Object> response = new HashMap<>();
        try {
            auditLogService.batchDeleteLogs(ids);
            response.put("success", true);
            response.put("message", "批量删除审计日志成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量删除审计日志失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 清理历史日志
     */
    @DeleteMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupLogs(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beforeTime) {
        Map<String, Object> response = new HashMap<>();
        try {
            int deletedCount = auditLogService.deleteLogsBefore(beforeTime);
            response.put("success", true);
            response.put("deletedCount", deletedCount);
            response.put("message", "历史日志清理成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "历史日志清理失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取审计日志统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", auditLogService.getTotalCount());
            statistics.put("loginCount", auditLogService.getCountByOperationType("LOGIN"));
            statistics.put("createCount", auditLogService.getCountByOperationType("CREATE"));
            statistics.put("updateCount", auditLogService.getCountByOperationType("UPDATE"));
            statistics.put("deleteCount", auditLogService.getCountByOperationType("DELETE"));
            statistics.put("selectCount", auditLogService.getCountByOperationType("SELECT"));
            statistics.put("successCount", auditLogService.getCountByResult("SUCCESS"));
            statistics.put("failureCount", auditLogService.getCountByResult("FAILURE"));
            statistics.put("partialCount", auditLogService.getCountByResult("PARTIAL"));
            
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "统计信息获取成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "统计信息获取失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取今日日志
     */
    @GetMapping("/today")
    public ResponseEntity<Map<String, Object>> getTodayLogs() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<AuditLog> logs = auditLogService.getTodayLogs();
            response.put("success", true);
            response.put("data", logs);
            response.put("message", "今日日志查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "今日日志查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取失败操作日志
     */
    @GetMapping("/failed")
    public ResponseEntity<Map<String, Object>> getFailedOperations() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<AuditLog> logs = auditLogService.getFailedOperations();
            response.put("success", true);
            response.put("data", logs);
            response.put("message", "失败操作日志查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "失败操作日志查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取用户操作统计
     */
    @GetMapping("/user-stats/{userId}")
    public ResponseEntity<Map<String, Object>> getUserOperationStats(
            @PathVariable Long userId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<AuditLog> logs = auditLogService.getUserOperationStats(userId, startTime, endTime);
            response.put("success", true);
            response.put("data", logs);
            response.put("message", "用户操作统计查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "用户操作统计查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取操作类型统计
     */
    @GetMapping("/operation-stats")
    public ResponseEntity<Map<String, Object>> getOperationTypeStats(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<AuditLog> logs = auditLogService.getOperationTypeStats(startTime, endTime);
            response.put("success", true);
            response.put("data", logs);
            response.put("message", "操作类型统计查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "操作类型统计查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
