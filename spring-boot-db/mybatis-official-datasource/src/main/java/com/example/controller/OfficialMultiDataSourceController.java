package com.example.controller;

import com.example.primary.domain.Account;
import com.example.primary.service.AccountService;
import com.example.secondary.domain.Transaction;
import com.example.secondary.service.TransactionService;
import com.example.third.domain.AuditLog;
import com.example.third.service.AuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 官方推荐多数据源统一控制器
 * 演示Spring Boot官方推荐的多数据源配置方式
 */
@RestController
@RequestMapping("/api/official-multi-datasource")
public class OfficialMultiDataSourceController {

    @Autowired
    private AccountService accountService;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private AuditLogService auditLogService;

    /**
     * 获取系统概览信息
     * 同时从三个数据源获取统计数据
     */
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getSystemOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        // 从主数据源获取账户统计信息
        Map<String, Object> accountStats = new HashMap<>();
        accountStats.put("totalAccounts", accountService.getTotalCount());
        accountStats.put("activeAccounts", accountService.getCountByStatus("ACTIVE"));
        accountStats.put("inactiveAccounts", accountService.getCountByStatus("INACTIVE"));
        accountStats.put("dataSource", "primary");
        
        // 从第二数据源获取交易统计信息
        Map<String, Object> transactionStats = new HashMap<>();
        transactionStats.put("totalTransactions", transactionService.getTotalCount());
        transactionStats.put("pendingTransactions", transactionService.getCountByStatus("PENDING"));
        transactionStats.put("completedTransactions", transactionService.getCountByStatus("COMPLETED"));
        transactionStats.put("dataSource", "secondary");
        
        // 从第三数据源获取审计日志统计信息
        Map<String, Object> auditStats = new HashMap<>();
        auditStats.put("totalLogs", auditLogService.getTotalCount());
        auditStats.put("successLogs", auditLogService.getCountByResult("SUCCESS"));
        auditStats.put("failureLogs", auditLogService.getCountByResult("FAILURE"));
        auditStats.put("dataSource", "third");
        
        overview.put("accountStatistics", accountStats);
        overview.put("transactionStatistics", transactionStats);
        overview.put("auditStatistics", auditStats);
        overview.put("message", "官方推荐多数据源统计信息获取成功");
        overview.put("configurationMethod", "DataSourceProperties + @ConfigurationProperties");
        
        return ResponseEntity.ok(overview);
    }

    /**
     * 获取最新数据
     * 从三个数据源分别获取最新的数据
     */
    @GetMapping("/latest")
    public ResponseEntity<Map<String, Object>> getLatestData() {
        Map<String, Object> latestData = new HashMap<>();
        
        // 从主数据源获取最新账户（前5个）
        List<Account> latestAccounts = accountService.getByPage(1, 5);
        
        // 从第二数据源获取最新交易（前5个）
        List<Transaction> latestTransactions = transactionService.getByPage(1, 5);
        
        // 从第三数据源获取最新审计日志（前5个）
        List<AuditLog> latestLogs = auditLogService.getByPage(1, 5);
        
        latestData.put("latestAccounts", latestAccounts);
        latestData.put("latestTransactions", latestTransactions);
        latestData.put("latestAuditLogs", latestLogs);
        latestData.put("message", "最新数据获取成功");
        latestData.put("note", "数据来自三个不同的数据源");
        
        return ResponseEntity.ok(latestData);
    }

    /**
     * 数据源健康检查
     * 检查三个数据源的连接状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> checkDataSourceHealth() {
        Map<String, Object> healthStatus = new HashMap<>();
        
        // 检查主数据源
        try {
            int accountCount = accountService.getTotalCount();
            Map<String, Object> primaryStatus = new HashMap<>();
            primaryStatus.put("status", "UP");
            primaryStatus.put("totalRecords", accountCount);
            primaryStatus.put("message", "主数据源连接正常");
            primaryStatus.put("dataSource", "primary");
            primaryStatus.put("database", "primary_db");
            healthStatus.put("primaryDataSource", primaryStatus);
        } catch (Exception e) {
            Map<String, Object> primaryStatus = new HashMap<>();
            primaryStatus.put("status", "DOWN");
            primaryStatus.put("error", e.getMessage());
            primaryStatus.put("message", "主数据源连接异常");
            primaryStatus.put("dataSource", "primary");
            healthStatus.put("primaryDataSource", primaryStatus);
        }
        
        // 检查第二数据源
        try {
            int transactionCount = transactionService.getTotalCount();
            Map<String, Object> secondaryStatus = new HashMap<>();
            secondaryStatus.put("status", "UP");
            secondaryStatus.put("totalRecords", transactionCount);
            secondaryStatus.put("message", "第二数据源连接正常");
            secondaryStatus.put("dataSource", "secondary");
            secondaryStatus.put("database", "secondary_db");
            healthStatus.put("secondaryDataSource", secondaryStatus);
        } catch (Exception e) {
            Map<String, Object> secondaryStatus = new HashMap<>();
            secondaryStatus.put("status", "DOWN");
            secondaryStatus.put("error", e.getMessage());
            secondaryStatus.put("message", "第二数据源连接异常");
            secondaryStatus.put("dataSource", "secondary");
            healthStatus.put("secondaryDataSource", secondaryStatus);
        }
        
        // 检查第三数据源
        try {
            int auditCount = auditLogService.getTotalCount();
            Map<String, Object> thirdStatus = new HashMap<>();
            thirdStatus.put("status", "UP");
            thirdStatus.put("totalRecords", auditCount);
            thirdStatus.put("message", "第三数据源连接正常");
            thirdStatus.put("dataSource", "third");
            thirdStatus.put("database", "third_db");
            healthStatus.put("thirdDataSource", thirdStatus);
        } catch (Exception e) {
            Map<String, Object> thirdStatus = new HashMap<>();
            thirdStatus.put("status", "DOWN");
            thirdStatus.put("error", e.getMessage());
            thirdStatus.put("message", "第三数据源连接异常");
            thirdStatus.put("dataSource", "third");
            healthStatus.put("thirdDataSource", thirdStatus);
        }
        
        return ResponseEntity.ok(healthStatus);
    }

    /**
     * 获取数据源配置信息
     * 展示官方推荐的配置方式
     */
    @GetMapping("/configuration-info")
    public ResponseEntity<Map<String, Object>> getConfigurationInfo() {
        Map<String, Object> configInfo = new HashMap<>();
        
        // 配置方式说明
        Map<String, Object> configMethod = new HashMap<>();
        configMethod.put("approach", "Spring Boot Official Recommendation");
        configMethod.put("primaryMethod", "DataSourceProperties + @ConfigurationProperties");
        configMethod.put("additionalMethod", "DataSourceProperties + @Qualifier + defaultCandidate=false");
        configMethod.put("benefits", List.of(
            "类型安全的配置绑定",
            "支持HikariCP完整配置选项",
            "遵循Spring Boot自动配置最佳实践",
            "IDE友好的配置提示",
            "易于维护和扩展"
        ));
        
        // 数据源信息
        Map<String, Object> dataSources = new HashMap<>();
        dataSources.put("primary", Map.of(
            "qualifier", "@Primary",
            "configPrefix", "spring.datasource",
            "database", "primary_db",
            "purpose", "账户管理"
        ));
        dataSources.put("secondary", Map.of(
            "qualifier", "@Qualifier(\"second\")",
            "configPrefix", "app.datasource",
            "database", "secondary_db",
            "purpose", "交易记录"
        ));
        dataSources.put("third", Map.of(
            "qualifier", "@Qualifier(\"third\")",
            "configPrefix", "app.third-datasource",
            "database", "third_db",
            "purpose", "审计日志"
        ));
        
        configInfo.put("configurationMethod", configMethod);
        configInfo.put("dataSources", dataSources);
        configInfo.put("documentation", "https://docs.spring.io/spring-boot/docs/current/reference/html/data.html#data.sql.datasource.multiple");
        
        return ResponseEntity.ok(configInfo);
    }

    /**
     * 演示跨数据源操作
     * 创建一个完整的业务流程，涉及三个数据源
     */
    @GetMapping("/demo-cross-datasource")
    public ResponseEntity<Map<String, Object>> demoCrossDataSourceOperation() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 从主数据源获取账户信息
            List<Account> accounts = accountService.getByPage(1, 3);
            
            // 2. 从第二数据源获取相关交易
            List<Transaction> transactions = transactionService.getByPage(1, 3);
            
            // 3. 记录审计日志到第三数据源
            AuditLog auditLog = new AuditLog();
            auditLog.setOperationType("CROSS_DATASOURCE_QUERY");
            auditLog.setOperationTarget("MULTI_DATASOURCE_DEMO");
            auditLog.setUserId(1L);
            auditLog.setUsername("system");
            auditLog.setOperationDetails("演示跨数据源查询操作");
            auditLog.setResult("SUCCESS");
            
            auditLogService.createLog(auditLog);
            
            result.put("accounts", accounts);
            result.put("transactions", transactions);
            result.put("auditLog", auditLog);
            result.put("message", "跨数据源操作演示成功");
            result.put("note", "此操作涉及三个不同的数据源");
            
        } catch (Exception e) {
            result.put("error", e.getMessage());
            result.put("message", "跨数据源操作演示失败");
        }
        
        return ResponseEntity.ok(result);
    }
}
