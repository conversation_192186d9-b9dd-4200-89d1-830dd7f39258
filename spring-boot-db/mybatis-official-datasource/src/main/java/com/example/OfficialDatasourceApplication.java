package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * MyBatis官方推荐多数据源应用启动类
 * 
 * 特性：
 * 1. 基于Spring Boot官方推荐的DataSourceProperties配置方式
 * 2. 使用@ConfigurationProperties进行类型安全的配置绑定
 * 3. 支持HikariCP连接池的完整配置
 * 4. 遵循Spring Boot自动配置最佳实践
 */
@SpringBootApplication
public class OfficialDatasourceApplication {

    public static void main(String[] args) {
        SpringApplication.run(OfficialDatasourceApplication.class, args);
    }

}
