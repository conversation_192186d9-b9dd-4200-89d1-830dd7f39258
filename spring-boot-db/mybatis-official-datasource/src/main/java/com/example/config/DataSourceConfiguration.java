package com.example.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

import javax.sql.DataSource;

/**
 * 数据源配置类 - 基于Spring Boot官方推荐方式
 * 
 * 特点：
 * 1. 使用DataSourceProperties进行类型安全的配置绑定
 * 2. 支持HikariCP的完整配置选项
 * 3. 遵循Spring Boot自动配置最佳实践
 * 4. 支持多个数据源的独立配置
 */
@Configuration(proxyBeanMethods = false)
public class DataSourceConfiguration {

    /**
     * 主数据源配置类
     * 使用Spring Boot默认的数据源配置，但也支持DataSourceProperties方式
     */
    @Configuration(proxyBeanMethods = false)
    @MapperScan(basePackages = "com.example.primary.mapper",
                sqlSessionTemplateRef = "primarySqlSessionTemplate")
    static class PrimaryDataSourceConfiguration {

        /**
         * 主数据源属性配置
         * 绑定spring.datasource.*配置
         */
        @Primary
        @Bean(name = "primaryDataSourceProperties")
        @ConfigurationProperties("spring.datasource")
        public DataSourceProperties primaryDataSourceProperties() {
            return new DataSourceProperties();
        }

        /**
         * 主数据源 - 使用DataSourceProperties方式
         * 支持完整的HikariCP配置
         */
        @Primary
        @Bean(name = "primaryDataSource")
        @ConfigurationProperties("spring.datasource.hikari")
        public HikariDataSource primaryDataSource(
                @Qualifier("primaryDataSourceProperties") DataSourceProperties properties) {
            HikariDataSource dataSource = properties.initializeDataSourceBuilder()
                    .type(HikariDataSource.class)
                    .build();

            // 测试环境下初始化数据库表结构
            if (isTestEnvironment()) {
                initializePrimaryDatabase(dataSource);
            }

            return dataSource;
        }

        /**
         * 主数据源SqlSessionFactory
         */
        @Primary
        @Bean(name = "primarySqlSessionFactory")
        public SqlSessionFactory primarySqlSessionFactory(@Qualifier("primaryDataSource") DataSource dataSource) 
                throws Exception {
            SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
            sessionFactory.setDataSource(dataSource);
            
            // 设置MyBatis配置
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            configuration.setCacheEnabled(true);
            configuration.setUseGeneratedKeys(true);
            configuration.setLazyLoadingEnabled(true);
            configuration.setAggressiveLazyLoading(false);
            sessionFactory.setConfiguration(configuration);
            
            // 设置映射文件位置
            sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath:mapper/primary/*.xml"));
            
            // 设置类型别名
            sessionFactory.setTypeAliasesPackage("com.example.primary.domain");
            
            return sessionFactory.getObject();
        }

        /**
         * 主数据源SqlSessionTemplate
         */
        @Primary
        @Bean(name = "primarySqlSessionTemplate")
        public SqlSessionTemplate primarySqlSessionTemplate(
                @Qualifier("primarySqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
            return new SqlSessionTemplate(sqlSessionFactory);
        }

        /**
         * 主数据源事务管理器
         */
        @Primary
        @Bean(name = "primaryTransactionManager")
        public PlatformTransactionManager primaryTransactionManager(
                @Qualifier("primaryDataSource") DataSource dataSource) {
            return new DataSourceTransactionManager(dataSource);
        }
    }

    /**
     * 第二个数据源配置类
     * 使用官方推荐的DataSourceProperties方式
     */
    @Configuration(proxyBeanMethods = false)
    @MapperScan(basePackages = "com.example.secondary.mapper", 
                sqlSessionTemplateRef = "secondarySqlSessionTemplate")
    static class SecondaryDataSourceConfiguration {

        /**
         * 第二个数据源属性配置
         * 绑定app.datasource.*配置
         */
        @Qualifier("second")
        @Bean(name = "secondaryDataSourceProperties", defaultCandidate = false)
        @ConfigurationProperties("app.datasource")
        public DataSourceProperties secondaryDataSourceProperties() {
            return new DataSourceProperties();
        }

        /**
         * 第二个数据源
         * 使用DataSourceProperties初始化，并绑定HikariCP特定配置
         */
        @Qualifier("second")
        @Bean(name = "secondaryDataSource", defaultCandidate = false)
        @ConfigurationProperties("app.datasource.configuration")
        public HikariDataSource secondaryDataSource(
                @Qualifier("secondaryDataSourceProperties") DataSourceProperties properties) {
            HikariDataSource dataSource = properties.initializeDataSourceBuilder()
                    .type(HikariDataSource.class)
                    .build();

            // 测试环境下初始化数据库表结构
            if (isTestEnvironment()) {
                initializeSecondaryDatabase(dataSource);
            }

            return dataSource;
        }

        /**
         * 第二个数据源SqlSessionFactory
         */
        @Bean(name = "secondarySqlSessionFactory")
        public SqlSessionFactory secondarySqlSessionFactory(
                @Qualifier("secondaryDataSource") DataSource dataSource) throws Exception {
            SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
            sessionFactory.setDataSource(dataSource);
            
            // 设置MyBatis配置
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            configuration.setCacheEnabled(true);
            configuration.setUseGeneratedKeys(true);
            configuration.setLazyLoadingEnabled(true);
            configuration.setAggressiveLazyLoading(false);
            sessionFactory.setConfiguration(configuration);
            
            // 设置映射文件位置
            sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath:mapper/secondary/*.xml"));
            
            // 设置类型别名
            sessionFactory.setTypeAliasesPackage("com.example.secondary.domain");
            
            return sessionFactory.getObject();
        }

        /**
         * 第二个数据源SqlSessionTemplate
         */
        @Bean(name = "secondarySqlSessionTemplate")
        public SqlSessionTemplate secondarySqlSessionTemplate(
                @Qualifier("secondarySqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
            return new SqlSessionTemplate(sqlSessionFactory);
        }

        /**
         * 第二个数据源事务管理器
         */
        @Bean(name = "secondaryTransactionManager")
        public PlatformTransactionManager secondaryTransactionManager(
                @Qualifier("secondaryDataSource") DataSource dataSource) {
            return new DataSourceTransactionManager(dataSource);
        }
    }

    /**
     * 第三个数据源配置类（可选）
     * 演示更多数据源的配置方式
     */
    @Configuration(proxyBeanMethods = false)
    @MapperScan(basePackages = "com.example.third.mapper", 
                sqlSessionTemplateRef = "thirdSqlSessionTemplate")
    static class ThirdDataSourceConfiguration {

        /**
         * 第三个数据源属性配置
         */
        @Qualifier("third")
        @Bean(name = "thirdDataSourceProperties", defaultCandidate = false)
        @ConfigurationProperties("app.third-datasource")
        public DataSourceProperties thirdDataSourceProperties() {
            return new DataSourceProperties();
        }

        /**
         * 第三个数据源
         */
        @Qualifier("third")
        @Bean(name = "thirdDataSource", defaultCandidate = false)
        @ConfigurationProperties("app.third-datasource.configuration")
        public HikariDataSource thirdDataSource(
                @Qualifier("thirdDataSourceProperties") DataSourceProperties properties) {
            HikariDataSource dataSource = properties.initializeDataSourceBuilder()
                    .type(HikariDataSource.class)
                    .build();

            // 测试环境下初始化数据库表结构
            if (isTestEnvironment()) {
                initializeThirdDatabase(dataSource);
            }

            return dataSource;
        }

        /**
         * 第三个数据源SqlSessionFactory
         */
        @Bean(name = "thirdSqlSessionFactory")
        public SqlSessionFactory thirdSqlSessionFactory(
                @Qualifier("thirdDataSource") DataSource dataSource) throws Exception {
            SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
            sessionFactory.setDataSource(dataSource);
            
            // 设置MyBatis配置
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            configuration.setCacheEnabled(true);
            configuration.setUseGeneratedKeys(true);
            sessionFactory.setConfiguration(configuration);
            
            // 设置映射文件位置
            sessionFactory.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath:mapper/third/*.xml"));
            
            // 设置类型别名
            sessionFactory.setTypeAliasesPackage("com.example.third.domain");
            
            return sessionFactory.getObject();
        }

        /**
         * 第三个数据源SqlSessionTemplate
         */
        @Bean(name = "thirdSqlSessionTemplate")
        public SqlSessionTemplate thirdSqlSessionTemplate(
                @Qualifier("thirdSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
            return new SqlSessionTemplate(sqlSessionFactory);
        }

        /**
         * 第三个数据源事务管理器
         */
        @Bean(name = "thirdTransactionManager")
        public PlatformTransactionManager thirdTransactionManager(
                @Qualifier("thirdDataSource") DataSource dataSource) {
            return new DataSourceTransactionManager(dataSource);
        }
    }

    /**
     * 检查是否为测试环境
     */
    private static boolean isTestEnvironment() {
        String[] activeProfiles = SpringApplication.getShutdownHandlers().toString().contains("test") ?
            new String[]{"test"} : new String[0];
        return activeProfiles.length > 0 && "test".equals(activeProfiles[0]);
    }

    /**
     * 初始化主数据源的数据库表结构（测试环境）
     */
    private static void initializePrimaryDatabase(HikariDataSource dataSource) {
        try (Connection connection = dataSource.getConnection()) {
            // 创建账户表
            String createTableSql = """
                CREATE TABLE IF NOT EXISTS accounts (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    account_no VARCHAR(50) NOT NULL UNIQUE,
                    account_name VARCHAR(100) NOT NULL,
                    email VARCHAR(100),
                    phone VARCHAR(20),
                    balance DECIMAL(15,2) DEFAULT 0.00,
                    account_type VARCHAR(20) DEFAULT 'SAVINGS',
                    status VARCHAR(20) DEFAULT 'ACTIVE',
                    description TEXT,
                    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """;

            try (PreparedStatement stmt = connection.prepareStatement(createTableSql)) {
                stmt.execute();
            }

            // 创建索引
            String[] indexes = {
                "CREATE INDEX IF NOT EXISTS idx_accounts_no ON accounts(account_no)",
                "CREATE INDEX IF NOT EXISTS idx_accounts_email ON accounts(email)",
                "CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(account_type)",
                "CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status)"
            };

            for (String indexSql : indexes) {
                try (PreparedStatement stmt = connection.prepareStatement(indexSql)) {
                    stmt.execute();
                }
            }

        } catch (SQLException e) {
            throw new RuntimeException("Failed to initialize primary database", e);
        }
    }

    /**
     * 初始化第二数据源的数据库表结构（测试环境）
     */
    private static void initializeSecondaryDatabase(HikariDataSource dataSource) {
        try (Connection connection = dataSource.getConnection()) {
            // 创建交易表
            String createTableSql = """
                CREATE TABLE IF NOT EXISTS transactions (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    transaction_no VARCHAR(50) NOT NULL UNIQUE,
                    account_id BIGINT NOT NULL,
                    account_no VARCHAR(50) NOT NULL,
                    amount DECIMAL(15,2) NOT NULL,
                    transaction_type VARCHAR(20) NOT NULL,
                    status VARCHAR(20) DEFAULT 'PENDING',
                    description TEXT,
                    reference_no VARCHAR(100),
                    transaction_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """;

            try (PreparedStatement stmt = connection.prepareStatement(createTableSql)) {
                stmt.execute();
            }

            // 创建索引
            String[] indexes = {
                "CREATE INDEX IF NOT EXISTS idx_transactions_no ON transactions(transaction_no)",
                "CREATE INDEX IF NOT EXISTS idx_transactions_account_id ON transactions(account_id)",
                "CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type)",
                "CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)"
            };

            for (String indexSql : indexes) {
                try (PreparedStatement stmt = connection.prepareStatement(indexSql)) {
                    stmt.execute();
                }
            }

        } catch (SQLException e) {
            throw new RuntimeException("Failed to initialize secondary database", e);
        }
    }

    /**
     * 初始化第三数据源的数据库表结构（测试环境）
     */
    private static void initializeThirdDatabase(HikariDataSource dataSource) {
        try (Connection connection = dataSource.getConnection()) {
            // 创建审计日志表
            String createTableSql = """
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    operation_type VARCHAR(50) NOT NULL,
                    operation_target VARCHAR(100) NOT NULL,
                    target_id BIGINT,
                    user_id BIGINT NOT NULL,
                    username VARCHAR(100) NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    operation_details TEXT,
                    result VARCHAR(20) DEFAULT 'SUCCESS',
                    error_message TEXT,
                    execution_time BIGINT,
                    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """;

            try (PreparedStatement stmt = connection.prepareStatement(createTableSql)) {
                stmt.execute();
            }

            // 创建索引
            String[] indexes = {
                "CREATE INDEX IF NOT EXISTS idx_audit_logs_operation_type ON audit_logs(operation_type)",
                "CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_audit_logs_result ON audit_logs(result)",
                "CREATE INDEX IF NOT EXISTS idx_audit_logs_operation_time ON audit_logs(operation_time)"
            };

            for (String indexSql : indexes) {
                try (PreparedStatement stmt = connection.prepareStatement(indexSql)) {
                    stmt.execute();
                }
            }

        } catch (SQLException e) {
            throw new RuntimeException("Failed to initialize third database", e);
        }
    }
}
