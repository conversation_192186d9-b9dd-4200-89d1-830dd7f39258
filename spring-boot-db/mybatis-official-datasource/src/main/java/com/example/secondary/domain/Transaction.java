package com.example.secondary.domain;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易实体类 - 第二数据源
 * 用于演示官方推荐的多数据源配置
 */
public class Transaction {
    
    private Long id;
    
    @NotBlank(message = "交易编号不能为空")
    private String transactionNo;
    
    @NotNull(message = "账户ID不能为空")
    private Long accountId;
    
    private String accountNo;
    
    @NotNull(message = "交易金额不能为空")
    @Positive(message = "交易金额必须为正数")
    private BigDecimal amount;
    
    @NotBlank(message = "交易类型不能为空")
    private String transactionType;
    
    private String status;
    
    private String description;
    
    private String referenceNo;
    
    private LocalDateTime transactionTime;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;

    public Transaction() {
    }

    public Transaction(String transactionNo, Long accountId, String accountNo, 
                      BigDecimal amount, String transactionType) {
        this.transactionNo = transactionNo;
        this.accountId = accountId;
        this.accountNo = accountNo;
        this.amount = amount;
        this.transactionType = transactionType;
        this.status = "PENDING";
        this.transactionTime = LocalDateTime.now();
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNo() {
        return transactionNo;
    }

    public void setTransactionNo(String transactionNo) {
        this.transactionNo = transactionNo;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public LocalDateTime getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(LocalDateTime transactionTime) {
        this.transactionTime = transactionTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Transaction{" +
                "id=" + id +
                ", transactionNo='" + transactionNo + '\'' +
                ", accountId=" + accountId +
                ", accountNo='" + accountNo + '\'' +
                ", amount=" + amount +
                ", transactionType='" + transactionType + '\'' +
                ", status='" + status + '\'' +
                ", description='" + description + '\'' +
                ", referenceNo='" + referenceNo + '\'' +
                ", transactionTime=" + transactionTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
