package com.example.secondary.controller;

import com.example.secondary.domain.Transaction;
import com.example.secondary.service.TransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 交易控制器 - 第二数据源
 * 提供交易相关的RESTful API
 */
@RestController
@RequestMapping("/api/transactions")
@CrossOrigin(origins = "*")
public class TransactionController {

    @Autowired
    private TransactionService transactionService;

    /**
     * 根据ID查询交易
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            Transaction transaction = transactionService.getById(id);
            if (transaction != null) {
                response.put("success", true);
                response.put("data", transaction);
                response.put("message", "查询成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "交易不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 根据交易编号查询交易
     */
    @GetMapping("/transaction-no/{transactionNo}")
    public ResponseEntity<Map<String, Object>> getByTransactionNo(@PathVariable String transactionNo) {
        Map<String, Object> response = new HashMap<>();
        try {
            Transaction transaction = transactionService.getByTransactionNo(transactionNo);
            if (transaction != null) {
                response.put("success", true);
                response.put("data", transaction);
                response.put("message", "查询成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "交易不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }

    /**
     * 查询交易列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getTransactions(
            @RequestParam(required = false) Long accountId,
            @RequestParam(required = false) String accountNo,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) BigDecimal minAmount,
            @RequestParam(required = false) BigDecimal maxAmount,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        Map<String, Object> response = new HashMap<>();
        try {
            List<Transaction> transactions;
            
            if (accountId != null) {
                transactions = transactionService.getByAccountId(accountId);
            } else if (accountNo != null) {
                transactions = transactionService.getByAccountNo(accountNo);
            } else if (transactionType != null) {
                transactions = transactionService.getByTransactionType(transactionType);
            } else if (status != null) {
                transactions = transactionService.getByStatus(status);
            } else if (minAmount != null || maxAmount != null) {
                transactions = transactionService.getByAmountRange(minAmount, maxAmount);
            } else if (startTime != null || endTime != null) {
                transactions = transactionService.getByTimeRange(startTime, endTime);
            } else if (page != null && size != null) {
                transactions = transactionService.getByPage(page, size);
            } else {
                transactions = transactionService.getAllTransactions();
            }
            
            response.put("success", true);
            response.put("data", transactions);
            response.put("total", transactionService.getTotalCount());
            response.put("message", "查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 创建交易
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createTransaction(@Valid @RequestBody Transaction transaction) {
        Map<String, Object> response = new HashMap<>();
        try {
            Transaction createdTransaction = transactionService.createTransaction(transaction);
            response.put("success", true);
            response.put("data", createdTransaction);
            response.put("message", "交易创建成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "交易创建失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 更新交易
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateTransaction(@PathVariable Long id, 
                                                               @Valid @RequestBody Transaction transaction) {
        Map<String, Object> response = new HashMap<>();
        try {
            transaction.setId(id);
            Transaction updatedTransaction = transactionService.updateTransaction(transaction);
            response.put("success", true);
            response.put("data", updatedTransaction);
            response.put("message", "交易更新成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "交易更新失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 更新交易状态
     */
    @PatchMapping("/{id}/status")
    public ResponseEntity<Map<String, Object>> updateStatus(@PathVariable Long id, 
                                                          @RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        try {
            String status = request.get("status");
            transactionService.updateStatus(id, status);
            response.put("success", true);
            response.put("message", "交易状态更新成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "交易状态更新失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除交易
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteTransaction(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            transactionService.deleteTransaction(id);
            response.put("success", true);
            response.put("message", "交易删除成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "交易删除失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量创建交易
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchCreateTransactions(@Valid @RequestBody List<Transaction> transactions) {
        Map<String, Object> response = new HashMap<>();
        try {
            transactionService.batchCreateTransactions(transactions);
            response.put("success", true);
            response.put("message", "批量创建交易成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量创建交易失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量删除交易
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteTransactions(@RequestBody List<Long> ids) {
        Map<String, Object> response = new HashMap<>();
        try {
            transactionService.batchDeleteTransactions(ids);
            response.put("success", true);
            response.put("message", "批量删除交易成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量删除交易失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取交易统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", transactionService.getTotalCount());
            statistics.put("pendingCount", transactionService.getCountByStatus("PENDING"));
            statistics.put("processingCount", transactionService.getCountByStatus("PROCESSING"));
            statistics.put("completedCount", transactionService.getCountByStatus("COMPLETED"));
            statistics.put("failedCount", transactionService.getCountByStatus("FAILED"));
            statistics.put("cancelledCount", transactionService.getCountByStatus("CANCELLED"));
            statistics.put("depositCount", transactionService.getCountByTransactionType("DEPOSIT"));
            statistics.put("withdrawCount", transactionService.getCountByTransactionType("WITHDRAW"));
            statistics.put("transferCount", transactionService.getCountByTransactionType("TRANSFER"));
            statistics.put("paymentCount", transactionService.getCountByTransactionType("PAYMENT"));
            statistics.put("totalAmount", transactionService.getTotalAmount());
            statistics.put("depositAmount", transactionService.getTotalAmountByType("DEPOSIT"));
            statistics.put("withdrawAmount", transactionService.getTotalAmountByType("WITHDRAW"));
            statistics.put("transferAmount", transactionService.getTotalAmountByType("TRANSFER"));
            statistics.put("paymentAmount", transactionService.getTotalAmountByType("PAYMENT"));
            
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "统计信息获取成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "统计信息获取失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取今日交易
     */
    @GetMapping("/today")
    public ResponseEntity<Map<String, Object>> getTodayTransactions() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Transaction> transactions = transactionService.getTodayTransactions();
            response.put("success", true);
            response.put("data", transactions);
            response.put("message", "今日交易查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "今日交易查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取本月交易
     */
    @GetMapping("/monthly")
    public ResponseEntity<Map<String, Object>> getMonthlyTransactions() {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Transaction> transactions = transactionService.getMonthlyTransactions();
            response.put("success", true);
            response.put("data", transactions);
            response.put("message", "本月交易查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "本月交易查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
