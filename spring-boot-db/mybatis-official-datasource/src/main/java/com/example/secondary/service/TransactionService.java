package com.example.secondary.service;

import com.example.secondary.domain.Transaction;
import com.example.secondary.mapper.TransactionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易服务类 - 第二数据源
 * 提供交易相关的业务逻辑处理
 */
@Service
@Transactional(transactionManager = "secondaryTransactionManager")
public class TransactionService {

    @Autowired
    private TransactionMapper transactionMapper;

    /**
     * 根据ID查询交易
     */
    public Transaction getById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("交易ID不能为空");
        }
        return transactionMapper.selectById(id);
    }

    /**
     * 根据交易编号查询交易
     */
    public Transaction getByTransactionNo(String transactionNo) {
        if (transactionNo == null || transactionNo.trim().isEmpty()) {
            throw new IllegalArgumentException("交易编号不能为空");
        }
        return transactionMapper.selectByTransactionNo(transactionNo);
    }

    /**
     * 查询所有交易
     */
    public List<Transaction> getAllTransactions() {
        return transactionMapper.selectAll();
    }

    /**
     * 根据账户ID查询交易
     */
    public List<Transaction> getByAccountId(Long accountId) {
        if (accountId == null) {
            throw new IllegalArgumentException("账户ID不能为空");
        }
        return transactionMapper.selectByAccountId(accountId);
    }

    /**
     * 根据账户编号查询交易
     */
    public List<Transaction> getByAccountNo(String accountNo) {
        if (accountNo == null || accountNo.trim().isEmpty()) {
            throw new IllegalArgumentException("账户编号不能为空");
        }
        return transactionMapper.selectByAccountNo(accountNo);
    }

    /**
     * 根据交易类型查询交易
     */
    public List<Transaction> getByTransactionType(String transactionType) {
        if (transactionType == null || transactionType.trim().isEmpty()) {
            throw new IllegalArgumentException("交易类型不能为空");
        }
        return transactionMapper.selectByTransactionType(transactionType);
    }

    /**
     * 根据状态查询交易
     */
    public List<Transaction> getByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("交易状态不能为空");
        }
        return transactionMapper.selectByStatus(status);
    }

    /**
     * 根据金额范围查询交易
     */
    public List<Transaction> getByAmountRange(BigDecimal minAmount, BigDecimal maxAmount) {
        if (minAmount != null && maxAmount != null && minAmount.compareTo(maxAmount) > 0) {
            throw new IllegalArgumentException("最小金额不能大于最大金额");
        }
        return transactionMapper.selectByAmountRange(minAmount, maxAmount);
    }

    /**
     * 根据时间范围查询交易
     */
    public List<Transaction> getByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        return transactionMapper.selectByTimeRange(startTime, endTime);
    }

    /**
     * 分页查询交易
     */
    public List<Transaction> getByPage(Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        Integer offset = (page - 1) * size;
        return transactionMapper.selectByPage(offset, size);
    }

    /**
     * 统计交易总数
     */
    public int getTotalCount() {
        return transactionMapper.countAll();
    }

    /**
     * 根据状态统计交易数
     */
    public int getCountByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("交易状态不能为空");
        }
        return transactionMapper.countByStatus(status);
    }

    /**
     * 根据交易类型统计交易数
     */
    public int getCountByTransactionType(String transactionType) {
        if (transactionType == null || transactionType.trim().isEmpty()) {
            throw new IllegalArgumentException("交易类型不能为空");
        }
        return transactionMapper.countByTransactionType(transactionType);
    }

    /**
     * 根据账户ID统计交易数
     */
    public int getCountByAccountId(Long accountId) {
        if (accountId == null) {
            throw new IllegalArgumentException("账户ID不能为空");
        }
        return transactionMapper.countByAccountId(accountId);
    }

    /**
     * 创建交易
     */
    public Transaction createTransaction(Transaction transaction) {
        if (transaction == null) {
            throw new IllegalArgumentException("交易信息不能为空");
        }
        
        // 验证必填字段
        validateTransaction(transaction);
        
        // 检查交易编号是否已存在
        if (transactionMapper.existsByTransactionNo(transaction.getTransactionNo())) {
            throw new IllegalArgumentException("交易编号已存在：" + transaction.getTransactionNo());
        }
        
        // 设置默认值
        if (transaction.getStatus() == null) {
            transaction.setStatus("PENDING");
        }
        if (transaction.getTransactionTime() == null) {
            transaction.setTransactionTime(LocalDateTime.now());
        }
        
        LocalDateTime now = LocalDateTime.now();
        transaction.setCreateTime(now);
        transaction.setUpdateTime(now);
        
        transactionMapper.insert(transaction);
        return transaction;
    }

    /**
     * 更新交易
     */
    public Transaction updateTransaction(Transaction transaction) {
        if (transaction == null || transaction.getId() == null) {
            throw new IllegalArgumentException("交易ID不能为空");
        }
        
        // 检查交易是否存在
        Transaction existingTransaction = transactionMapper.selectById(transaction.getId());
        if (existingTransaction == null) {
            throw new IllegalArgumentException("交易不存在：" + transaction.getId());
        }
        
        // 如果更新交易编号，检查是否重复
        if (transaction.getTransactionNo() != null && 
            !transaction.getTransactionNo().equals(existingTransaction.getTransactionNo()) &&
            transactionMapper.existsByTransactionNo(transaction.getTransactionNo())) {
            throw new IllegalArgumentException("交易编号已存在：" + transaction.getTransactionNo());
        }
        
        transaction.setUpdateTime(LocalDateTime.now());
        transactionMapper.updateByIdSelective(transaction);
        
        return transactionMapper.selectById(transaction.getId());
    }

    /**
     * 更新交易状态
     */
    public void updateStatus(Long id, String status) {
        if (id == null) {
            throw new IllegalArgumentException("交易ID不能为空");
        }
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("交易状态不能为空");
        }
        
        // 检查交易是否存在
        Transaction transaction = transactionMapper.selectById(id);
        if (transaction == null) {
            throw new IllegalArgumentException("交易不存在：" + id);
        }
        
        // 验证状态转换的合法性
        validateStatusTransition(transaction.getStatus(), status);
        
        transactionMapper.updateStatus(id, status);
    }

    /**
     * 删除交易
     */
    public void deleteTransaction(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("交易ID不能为空");
        }
        
        // 检查交易是否存在
        Transaction transaction = transactionMapper.selectById(id);
        if (transaction == null) {
            throw new IllegalArgumentException("交易不存在：" + id);
        }
        
        // 只有待处理或失败的交易才能删除
        if (!"PENDING".equals(transaction.getStatus()) && !"FAILED".equals(transaction.getStatus())) {
            throw new IllegalArgumentException("只有待处理或失败的交易才能删除");
        }
        
        transactionMapper.deleteById(id);
    }

    /**
     * 批量创建交易
     */
    public void batchCreateTransactions(List<Transaction> transactions) {
        if (transactions == null || transactions.isEmpty()) {
            throw new IllegalArgumentException("交易列表不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        for (Transaction transaction : transactions) {
            validateTransaction(transaction);
            
            // 检查交易编号是否已存在
            if (transactionMapper.existsByTransactionNo(transaction.getTransactionNo())) {
                throw new IllegalArgumentException("交易编号已存在：" + transaction.getTransactionNo());
            }
            
            // 设置默认值
            if (transaction.getStatus() == null) {
                transaction.setStatus("PENDING");
            }
            if (transaction.getTransactionTime() == null) {
                transaction.setTransactionTime(now);
            }
            
            transaction.setCreateTime(now);
            transaction.setUpdateTime(now);
        }
        
        transactionMapper.batchInsert(transactions);
    }

    /**
     * 批量删除交易
     */
    public void batchDeleteTransactions(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("交易ID列表不能为空");
        }
        
        // 检查所有交易是否存在且状态允许删除
        for (Long id : ids) {
            Transaction transaction = transactionMapper.selectById(id);
            if (transaction == null) {
                throw new IllegalArgumentException("交易不存在：" + id);
            }
            if (!"PENDING".equals(transaction.getStatus()) && !"FAILED".equals(transaction.getStatus())) {
                throw new IllegalArgumentException("只有待处理或失败的交易才能删除：" + id);
            }
        }
        
        transactionMapper.batchDelete(ids);
    }

    /**
     * 获取交易金额总和
     */
    public BigDecimal getTotalAmount() {
        return transactionMapper.getTotalAmount();
    }

    /**
     * 根据交易类型获取金额总和
     */
    public BigDecimal getTotalAmountByType(String transactionType) {
        if (transactionType == null || transactionType.trim().isEmpty()) {
            throw new IllegalArgumentException("交易类型不能为空");
        }
        return transactionMapper.getTotalAmountByType(transactionType);
    }

    /**
     * 根据账户ID获取交易金额总和
     */
    public BigDecimal getTotalAmountByAccountId(Long accountId) {
        if (accountId == null) {
            throw new IllegalArgumentException("账户ID不能为空");
        }
        return transactionMapper.getTotalAmountByAccountId(accountId);
    }

    /**
     * 获取今日交易统计
     */
    public List<Transaction> getTodayTransactions() {
        return transactionMapper.getTodayTransactions();
    }

    /**
     * 获取本月交易统计
     */
    public List<Transaction> getMonthlyTransactions() {
        return transactionMapper.getMonthlyTransactions();
    }

    /**
     * 验证交易信息
     */
    private void validateTransaction(Transaction transaction) {
        if (transaction.getTransactionNo() == null || transaction.getTransactionNo().trim().isEmpty()) {
            throw new IllegalArgumentException("交易编号不能为空");
        }
        if (transaction.getAccountId() == null) {
            throw new IllegalArgumentException("账户ID不能为空");
        }
        if (transaction.getAmount() == null) {
            throw new IllegalArgumentException("交易金额不能为空");
        }
        if (transaction.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("交易金额必须大于零");
        }
        if (transaction.getTransactionType() == null || transaction.getTransactionType().trim().isEmpty()) {
            throw new IllegalArgumentException("交易类型不能为空");
        }
    }

    /**
     * 验证状态转换的合法性
     */
    private void validateStatusTransition(String currentStatus, String newStatus) {
        // 简单的状态转换验证
        if ("COMPLETED".equals(currentStatus)) {
            throw new IllegalArgumentException("已完成的交易不能修改状态");
        }
        if ("CANCELLED".equals(currentStatus)) {
            throw new IllegalArgumentException("已取消的交易不能修改状态");
        }
    }
}
