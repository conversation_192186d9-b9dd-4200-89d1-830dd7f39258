package com.example.secondary.mapper;

import com.example.secondary.domain.Transaction;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易Mapper接口 - 第二数据源
 * 使用XML映射文件配置SQL
 */
@Repository
public interface TransactionMapper {

    /**
     * 根据ID查询交易
     */
    Transaction selectById(Long id);

    /**
     * 根据交易编号查询交易
     */
    Transaction selectByTransactionNo(String transactionNo);

    /**
     * 查询所有交易
     */
    List<Transaction> selectAll();

    /**
     * 根据账户ID查询交易
     */
    List<Transaction> selectByAccountId(Long accountId);

    /**
     * 根据账户编号查询交易
     */
    List<Transaction> selectByAccountNo(String accountNo);

    /**
     * 根据交易类型查询交易
     */
    List<Transaction> selectByTransactionType(String transactionType);

    /**
     * 根据状态查询交易
     */
    List<Transaction> selectByStatus(String status);

    /**
     * 根据金额范围查询交易
     */
    List<Transaction> selectByAmountRange(@Param("minAmount") BigDecimal minAmount, 
                                         @Param("maxAmount") BigDecimal maxAmount);

    /**
     * 根据时间范围查询交易
     */
    List<Transaction> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询交易
     */
    List<Transaction> selectByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 统计交易总数
     */
    int countAll();

    /**
     * 根据状态统计交易数
     */
    int countByStatus(String status);

    /**
     * 根据交易类型统计交易数
     */
    int countByTransactionType(String transactionType);

    /**
     * 根据账户ID统计交易数
     */
    int countByAccountId(Long accountId);

    /**
     * 插入交易
     */
    int insert(Transaction transaction);

    /**
     * 选择性插入交易
     */
    int insertSelective(Transaction transaction);

    /**
     * 根据ID更新交易
     */
    int updateById(Transaction transaction);

    /**
     * 选择性更新交易
     */
    int updateByIdSelective(Transaction transaction);

    /**
     * 更新交易状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 根据ID删除交易
     */
    int deleteById(Long id);

    /**
     * 批量插入交易
     */
    int batchInsert(List<Transaction> transactions);

    /**
     * 批量删除交易
     */
    int batchDelete(List<Long> ids);

    /**
     * 根据交易编号检查是否存在
     */
    boolean existsByTransactionNo(String transactionNo);

    /**
     * 获取交易金额总和
     */
    BigDecimal getTotalAmount();

    /**
     * 根据交易类型获取金额总和
     */
    BigDecimal getTotalAmountByType(String transactionType);

    /**
     * 根据账户ID获取交易金额总和
     */
    BigDecimal getTotalAmountByAccountId(Long accountId);

    /**
     * 获取今日交易统计
     */
    List<Transaction> getTodayTransactions();

    /**
     * 获取本月交易统计
     */
    List<Transaction> getMonthlyTransactions();
}
