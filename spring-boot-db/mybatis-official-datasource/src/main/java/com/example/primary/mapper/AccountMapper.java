package com.example.primary.mapper;

import com.example.primary.domain.Account;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账户Mapper接口 - 主数据源
 * 使用XML映射文件配置SQL
 */
@Repository
public interface AccountMapper {

    /**
     * 根据ID查询账户
     */
    Account selectById(Long id);

    /**
     * 根据账户编号查询账户
     */
    Account selectByAccountNo(String accountNo);

    /**
     * 查询所有账户
     */
    List<Account> selectAll();

    /**
     * 根据账户类型查询账户
     */
    List<Account> selectByAccountType(String accountType);

    /**
     * 根据状态查询账户
     */
    List<Account> selectByStatus(String status);

    /**
     * 根据余额范围查询账户
     */
    List<Account> selectByBalanceRange(@Param("minBalance") BigDecimal minBalance, 
                                      @Param("maxBalance") BigDecimal maxBalance);

    /**
     * 分页查询账户
     */
    List<Account> selectByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 统计账户总数
     */
    int countAll();

    /**
     * 根据状态统计账户数
     */
    int countByStatus(String status);

    /**
     * 根据账户类型统计账户数
     */
    int countByAccountType(String accountType);

    /**
     * 插入账户
     */
    int insert(Account account);

    /**
     * 选择性插入账户
     */
    int insertSelective(Account account);

    /**
     * 根据ID更新账户
     */
    int updateById(Account account);

    /**
     * 选择性更新账户
     */
    int updateByIdSelective(Account account);

    /**
     * 更新账户余额
     */
    int updateBalance(@Param("id") Long id, @Param("balance") BigDecimal balance);

    /**
     * 根据ID删除账户
     */
    int deleteById(Long id);

    /**
     * 批量插入账户
     */
    int batchInsert(List<Account> accounts);

    /**
     * 批量删除账户
     */
    int batchDelete(List<Long> ids);

    /**
     * 根据账户编号检查是否存在
     */
    boolean existsByAccountNo(String accountNo);

    /**
     * 获取账户余额总和
     */
    BigDecimal getTotalBalance();

    /**
     * 根据账户类型获取余额总和
     */
    BigDecimal getTotalBalanceByType(String accountType);
}
