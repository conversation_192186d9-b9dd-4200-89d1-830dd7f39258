package com.example.primary.controller;

import com.example.primary.domain.Account;
import com.example.primary.service.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账户控制器 - 主数据源
 * 提供账户相关的RESTful API
 */
@RestController
@RequestMapping("/api/accounts")
@CrossOrigin(origins = "*")
public class AccountController {

    @Autowired
    private AccountService accountService;

    /**
     * 根据ID查询账户
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            Account account = accountService.getById(id);
            if (account != null) {
                response.put("success", true);
                response.put("data", account);
                response.put("message", "查询成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "账户不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 根据账户编号查询账户
     */
    @GetMapping("/account-no/{accountNo}")
    public ResponseEntity<Map<String, Object>> getByAccountNo(@PathVariable String accountNo) {
        Map<String, Object> response = new HashMap<>();
        try {
            Account account = accountService.getByAccountNo(accountNo);
            if (account != null) {
                response.put("success", true);
                response.put("data", account);
                response.put("message", "查询成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "账户不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }

    /**
     * 查询所有账户
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllAccounts(
            @RequestParam(required = false) String accountType,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) BigDecimal minBalance,
            @RequestParam(required = false) BigDecimal maxBalance,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        Map<String, Object> response = new HashMap<>();
        try {
            List<Account> accounts;
            
            if (accountType != null) {
                accounts = accountService.getByAccountType(accountType);
            } else if (status != null) {
                accounts = accountService.getByStatus(status);
            } else if (minBalance != null || maxBalance != null) {
                accounts = accountService.getByBalanceRange(minBalance, maxBalance);
            } else if (page != null && size != null) {
                accounts = accountService.getByPage(page, size);
            } else {
                accounts = accountService.getAllAccounts();
            }
            
            response.put("success", true);
            response.put("data", accounts);
            response.put("total", accountService.getTotalCount());
            response.put("message", "查询成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 创建账户
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createAccount(@Valid @RequestBody Account account) {
        Map<String, Object> response = new HashMap<>();
        try {
            Account createdAccount = accountService.createAccount(account);
            response.put("success", true);
            response.put("data", createdAccount);
            response.put("message", "账户创建成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "账户创建失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 更新账户
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateAccount(@PathVariable Long id, 
                                                           @Valid @RequestBody Account account) {
        Map<String, Object> response = new HashMap<>();
        try {
            account.setId(id);
            Account updatedAccount = accountService.updateAccount(account);
            response.put("success", true);
            response.put("data", updatedAccount);
            response.put("message", "账户更新成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "账户更新失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 更新账户余额
     */
    @PatchMapping("/{id}/balance")
    public ResponseEntity<Map<String, Object>> updateBalance(@PathVariable Long id, 
                                                           @RequestBody Map<String, BigDecimal> request) {
        Map<String, Object> response = new HashMap<>();
        try {
            BigDecimal balance = request.get("balance");
            accountService.updateBalance(id, balance);
            response.put("success", true);
            response.put("message", "余额更新成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "余额更新失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除账户
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteAccount(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            accountService.deleteAccount(id);
            response.put("success", true);
            response.put("message", "账户删除成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "账户删除失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量创建账户
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchCreateAccounts(@Valid @RequestBody List<Account> accounts) {
        Map<String, Object> response = new HashMap<>();
        try {
            accountService.batchCreateAccounts(accounts);
            response.put("success", true);
            response.put("message", "批量创建账户成功");
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量创建账户失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量删除账户
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteAccounts(@RequestBody List<Long> ids) {
        Map<String, Object> response = new HashMap<>();
        try {
            accountService.batchDeleteAccounts(ids);
            response.put("success", true);
            response.put("message", "批量删除账户成功");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量删除账户失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取账户统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", accountService.getTotalCount());
            statistics.put("activeCount", accountService.getCountByStatus("ACTIVE"));
            statistics.put("inactiveCount", accountService.getCountByStatus("INACTIVE"));
            statistics.put("frozenCount", accountService.getCountByStatus("FROZEN"));
            statistics.put("savingsCount", accountService.getCountByAccountType("SAVINGS"));
            statistics.put("checkingCount", accountService.getCountByAccountType("CHECKING"));
            statistics.put("creditCount", accountService.getCountByAccountType("CREDIT"));
            statistics.put("totalBalance", accountService.getTotalBalance());
            statistics.put("savingsBalance", accountService.getTotalBalanceByType("SAVINGS"));
            statistics.put("checkingBalance", accountService.getTotalBalanceByType("CHECKING"));
            statistics.put("creditBalance", accountService.getTotalBalanceByType("CREDIT"));
            
            response.put("success", true);
            response.put("data", statistics);
            response.put("message", "统计信息获取成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "统计信息获取失败：" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
