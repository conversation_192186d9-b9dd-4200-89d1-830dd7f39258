package com.example.primary.service;

import com.example.primary.domain.Account;
import com.example.primary.mapper.AccountMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账户服务类 - 主数据源
 * 提供账户相关的业务逻辑处理
 */
@Service
@Transactional(transactionManager = "primaryTransactionManager")
public class AccountService {

    @Autowired
    private AccountMapper accountMapper;

    /**
     * 根据ID查询账户
     */
    public Account getById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("账户ID不能为空");
        }
        return accountMapper.selectById(id);
    }

    /**
     * 根据账户编号查询账户
     */
    public Account getByAccountNo(String accountNo) {
        if (accountNo == null || accountNo.trim().isEmpty()) {
            throw new IllegalArgumentException("账户编号不能为空");
        }
        return accountMapper.selectByAccountNo(accountNo);
    }

    /**
     * 查询所有账户
     */
    public List<Account> getAllAccounts() {
        return accountMapper.selectAll();
    }

    /**
     * 根据账户类型查询账户
     */
    public List<Account> getByAccountType(String accountType) {
        if (accountType == null || accountType.trim().isEmpty()) {
            throw new IllegalArgumentException("账户类型不能为空");
        }
        return accountMapper.selectByAccountType(accountType);
    }

    /**
     * 根据状态查询账户
     */
    public List<Account> getByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("账户状态不能为空");
        }
        return accountMapper.selectByStatus(status);
    }

    /**
     * 根据余额范围查询账户
     */
    public List<Account> getByBalanceRange(BigDecimal minBalance, BigDecimal maxBalance) {
        if (minBalance != null && maxBalance != null && minBalance.compareTo(maxBalance) > 0) {
            throw new IllegalArgumentException("最小余额不能大于最大余额");
        }
        return accountMapper.selectByBalanceRange(minBalance, maxBalance);
    }

    /**
     * 分页查询账户
     */
    public List<Account> getByPage(Integer page, Integer size) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        Integer offset = (page - 1) * size;
        return accountMapper.selectByPage(offset, size);
    }

    /**
     * 统计账户总数
     */
    public int getTotalCount() {
        return accountMapper.countAll();
    }

    /**
     * 根据状态统计账户数
     */
    public int getCountByStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("账户状态不能为空");
        }
        return accountMapper.countByStatus(status);
    }

    /**
     * 根据账户类型统计账户数
     */
    public int getCountByAccountType(String accountType) {
        if (accountType == null || accountType.trim().isEmpty()) {
            throw new IllegalArgumentException("账户类型不能为空");
        }
        return accountMapper.countByAccountType(accountType);
    }

    /**
     * 创建账户
     */
    public Account createAccount(Account account) {
        if (account == null) {
            throw new IllegalArgumentException("账户信息不能为空");
        }
        
        // 验证必填字段
        validateAccount(account);
        
        // 检查账户编号是否已存在
        if (accountMapper.existsByAccountNo(account.getAccountNo())) {
            throw new IllegalArgumentException("账户编号已存在：" + account.getAccountNo());
        }
        
        // 设置默认值
        if (account.getStatus() == null) {
            account.setStatus("ACTIVE");
        }
        if (account.getAccountType() == null) {
            account.setAccountType("SAVINGS");
        }
        if (account.getBalance() == null) {
            account.setBalance(BigDecimal.ZERO);
        }
        
        LocalDateTime now = LocalDateTime.now();
        account.setCreateTime(now);
        account.setUpdateTime(now);
        
        accountMapper.insert(account);
        return account;
    }

    /**
     * 更新账户
     */
    public Account updateAccount(Account account) {
        if (account == null || account.getId() == null) {
            throw new IllegalArgumentException("账户ID不能为空");
        }
        
        // 检查账户是否存在
        Account existingAccount = accountMapper.selectById(account.getId());
        if (existingAccount == null) {
            throw new IllegalArgumentException("账户不存在：" + account.getId());
        }
        
        // 如果更新账户编号，检查是否重复
        if (account.getAccountNo() != null && 
            !account.getAccountNo().equals(existingAccount.getAccountNo()) &&
            accountMapper.existsByAccountNo(account.getAccountNo())) {
            throw new IllegalArgumentException("账户编号已存在：" + account.getAccountNo());
        }
        
        account.setUpdateTime(LocalDateTime.now());
        accountMapper.updateByIdSelective(account);
        
        return accountMapper.selectById(account.getId());
    }

    /**
     * 更新账户余额
     */
    public void updateBalance(Long id, BigDecimal balance) {
        if (id == null) {
            throw new IllegalArgumentException("账户ID不能为空");
        }
        if (balance == null) {
            throw new IllegalArgumentException("余额不能为空");
        }
        if (balance.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("余额不能为负数");
        }
        
        // 检查账户是否存在
        Account account = accountMapper.selectById(id);
        if (account == null) {
            throw new IllegalArgumentException("账户不存在：" + id);
        }
        
        accountMapper.updateBalance(id, balance);
    }

    /**
     * 删除账户
     */
    public void deleteAccount(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("账户ID不能为空");
        }
        
        // 检查账户是否存在
        Account account = accountMapper.selectById(id);
        if (account == null) {
            throw new IllegalArgumentException("账户不存在：" + id);
        }
        
        // 检查账户余额
        if (account.getBalance() != null && account.getBalance().compareTo(BigDecimal.ZERO) > 0) {
            throw new IllegalArgumentException("账户余额不为零，无法删除");
        }
        
        accountMapper.deleteById(id);
    }

    /**
     * 批量创建账户
     */
    public void batchCreateAccounts(List<Account> accounts) {
        if (accounts == null || accounts.isEmpty()) {
            throw new IllegalArgumentException("账户列表不能为空");
        }
        
        LocalDateTime now = LocalDateTime.now();
        for (Account account : accounts) {
            validateAccount(account);
            
            // 检查账户编号是否已存在
            if (accountMapper.existsByAccountNo(account.getAccountNo())) {
                throw new IllegalArgumentException("账户编号已存在：" + account.getAccountNo());
            }
            
            // 设置默认值
            if (account.getStatus() == null) {
                account.setStatus("ACTIVE");
            }
            if (account.getAccountType() == null) {
                account.setAccountType("SAVINGS");
            }
            if (account.getBalance() == null) {
                account.setBalance(BigDecimal.ZERO);
            }
            
            account.setCreateTime(now);
            account.setUpdateTime(now);
        }
        
        accountMapper.batchInsert(accounts);
    }

    /**
     * 批量删除账户
     */
    public void batchDeleteAccounts(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("账户ID列表不能为空");
        }
        
        // 检查所有账户是否存在且余额为零
        for (Long id : ids) {
            Account account = accountMapper.selectById(id);
            if (account == null) {
                throw new IllegalArgumentException("账户不存在：" + id);
            }
            if (account.getBalance() != null && account.getBalance().compareTo(BigDecimal.ZERO) > 0) {
                throw new IllegalArgumentException("账户余额不为零，无法删除：" + id);
            }
        }
        
        accountMapper.batchDelete(ids);
    }

    /**
     * 获取账户余额总和
     */
    public BigDecimal getTotalBalance() {
        return accountMapper.getTotalBalance();
    }

    /**
     * 根据账户类型获取余额总和
     */
    public BigDecimal getTotalBalanceByType(String accountType) {
        if (accountType == null || accountType.trim().isEmpty()) {
            throw new IllegalArgumentException("账户类型不能为空");
        }
        return accountMapper.getTotalBalanceByType(accountType);
    }

    /**
     * 验证账户信息
     */
    private void validateAccount(Account account) {
        if (account.getAccountNo() == null || account.getAccountNo().trim().isEmpty()) {
            throw new IllegalArgumentException("账户编号不能为空");
        }
        if (account.getAccountName() == null || account.getAccountName().trim().isEmpty()) {
            throw new IllegalArgumentException("账户名称不能为空");
        }
        if (account.getEmail() == null || account.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("邮箱不能为空");
        }
        
        // 简单的邮箱格式验证
        if (!account.getEmail().contains("@")) {
            throw new IllegalArgumentException("邮箱格式不正确");
        }
        
        // 验证余额
        if (account.getBalance() != null && account.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("账户余额不能为负数");
        }
    }
}
