-- 第二数据库初始化脚本
-- 数据库: secondary_db

CREATE DATABASE IF NOT EXISTS secondary_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE secondary_db;

-- 创建交易表
CREATE TABLE IF NOT EXISTS transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '交易ID',
    transaction_no VARCHAR(50) NOT NULL UNIQUE COMMENT '交易编号',
    account_id BIGINT NOT NULL COMMENT '账户ID',
    account_no VARCHAR(50) NOT NULL COMMENT '账户编号',
    amount DECIMAL(15,2) NOT NULL COMMENT '交易金额',
    transaction_type VARCHAR(20) NOT NULL COMMENT '交易类型：DEPOSIT-存款，WITHDRAW-取款，TRANSFER-转账，PAYMENT-支付',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PENDING-待处理，PROCESSING-处理中，COMPLETED-已完成，FAILED-失败，CANCELLED-已取消',
    description TEXT COMMENT '交易描述',
    reference_no VARCHAR(100) COMMENT '参考编号',
    transaction_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '交易时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='交易表';

-- 创建索引
CREATE INDEX idx_transactions_no ON transactions(transaction_no);
CREATE INDEX idx_transactions_account_id ON transactions(account_id);
CREATE INDEX idx_transactions_account_no ON transactions(account_no);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_time ON transactions(transaction_time);
CREATE INDEX idx_transactions_create_time ON transactions(create_time);

-- 插入测试数据
INSERT INTO transactions (transaction_no, account_id, account_no, amount, transaction_type, status, description, reference_no, transaction_time) VALUES
('TXN001', 1, 'ACC001', 1000.00, 'DEPOSIT', 'COMPLETED', '工资存款', 'SAL202401001', '2024-01-15 09:30:00'),
('TXN002', 1, 'ACC001', 200.00, 'WITHDRAW', 'COMPLETED', 'ATM取款', 'ATM202401001', '2024-01-15 14:20:00'),
('TXN003', 2, 'ACC002', 500.00, 'DEPOSIT', 'COMPLETED', '转账收入', 'TRF202401001', '2024-01-16 10:15:00'),
('TXN004', 2, 'ACC002', 150.00, 'PAYMENT', 'COMPLETED', '在线购物支付', 'PAY202401001', '2024-01-16 16:45:00'),
('TXN005', 3, 'ACC003', 2000.00, 'DEPOSIT', 'COMPLETED', '投资收益', 'INV202401001', '2024-01-17 11:30:00'),
('TXN006', 3, 'ACC003', 800.00, 'TRANSFER', 'COMPLETED', '转账给朋友', 'TRF202401002', '2024-01-17 15:20:00'),
('TXN007', 4, 'ACC004', 300.00, 'WITHDRAW', 'FAILED', '余额不足', 'ATM202401002', '2024-01-18 09:45:00'),
('TXN008', 5, 'ACC005', 5000.00, 'DEPOSIT', 'COMPLETED', '企业收款', 'BIZ202401001', '2024-01-18 13:15:00'),
('TXN009', 5, 'ACC005', 1200.00, 'PAYMENT', 'COMPLETED', '供应商付款', 'PAY202401002', '2024-01-18 17:30:00'),
('TXN010', 6, 'ACC006', 3000.00, 'DEPOSIT', 'COMPLETED', '理财产品到期', 'FIN202401001', '2024-01-19 10:00:00'),
('TXN011', 6, 'ACC006', 500.00, 'WITHDRAW', 'COMPLETED', '生活费取款', 'ATM202401003', '2024-01-19 14:30:00'),
('TXN012', 7, 'ACC007', 100.00, 'DEPOSIT', 'COMPLETED', '零花钱存款', 'POC202401001', '2024-01-20 08:45:00'),
('TXN013', 8, 'ACC008', 10000.00, 'DEPOSIT', 'COMPLETED', 'VIP大额存款', 'VIP202401001', '2024-01-20 11:20:00'),
('TXN014', 8, 'ACC008', 2500.00, 'TRANSFER', 'COMPLETED', 'VIP转账服务', 'TRF202401003', '2024-01-20 16:15:00'),
('TXN015', 9, 'ACC009', 100.00, 'WITHDRAW', 'CANCELLED', '账户冻结', 'ATM202401004', '2024-01-21 09:30:00'),
('TXN016', 10, 'ACC010', 1500.00, 'DEPOSIT', 'COMPLETED', '联名账户存款', 'JNT202401001', '2024-01-21 13:45:00'),
('TXN017', 1, 'ACC001', 300.00, 'PAYMENT', 'PROCESSING', '信用卡还款', 'CRD202401001', '2024-01-22 10:30:00'),
('TXN018', 2, 'ACC002', 250.00, 'WITHDRAW', 'PENDING', '预约取款', 'RSV202401001', '2024-01-22 15:00:00'),
('TXN019', 3, 'ACC003', 1800.00, 'TRANSFER', 'COMPLETED', '房租转账', 'RNT202401001', '2024-01-23 09:15:00'),
('TXN020', 4, 'ACC004', 50.00, 'DEPOSIT', 'COMPLETED', '利息收入', 'INT202401001', '2024-01-23 12:00:00');

-- 查看插入结果
SELECT '交易统计' as 统计类型, COUNT(*) as 总数 FROM transactions;
SELECT '交易类型统计' as 统计类型, transaction_type as 类型, COUNT(*) as 数量 FROM transactions GROUP BY transaction_type;
SELECT '交易状态统计' as 统计类型, status as 状态, COUNT(*) as 数量 FROM transactions GROUP BY status;
SELECT '交易金额统计' as 统计类型, SUM(amount) as 总金额, AVG(amount) as 平均金额 FROM transactions WHERE status = 'COMPLETED';
SELECT '账户交易统计' as 统计类型, account_no as 账户, COUNT(*) as 交易次数, SUM(amount) as 总金额 FROM transactions WHERE status = 'COMPLETED' GROUP BY account_no ORDER BY 总金额 DESC;

-- 创建交易汇总视图
CREATE VIEW transaction_summary AS
SELECT 
    account_no,
    COUNT(*) as total_transactions,
    SUM(CASE WHEN transaction_type = 'DEPOSIT' THEN amount ELSE 0 END) as total_deposits,
    SUM(CASE WHEN transaction_type = 'WITHDRAW' THEN amount ELSE 0 END) as total_withdrawals,
    SUM(CASE WHEN transaction_type = 'TRANSFER' THEN amount ELSE 0 END) as total_transfers,
    SUM(CASE WHEN transaction_type = 'PAYMENT' THEN amount ELSE 0 END) as total_payments,
    SUM(CASE WHEN status = 'COMPLETED' THEN amount ELSE 0 END) as completed_amount,
    MAX(transaction_time) as last_transaction_time
FROM transactions 
GROUP BY account_no;

-- 查看汇总视图
SELECT '交易汇总视图' as 视图名称;
SELECT * FROM transaction_summary ORDER BY completed_amount DESC;
