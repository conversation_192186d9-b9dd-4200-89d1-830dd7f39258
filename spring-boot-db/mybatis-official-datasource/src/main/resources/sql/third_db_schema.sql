-- 第三数据库初始化脚本
-- 数据库: third_db

CREATE DATABASE IF NOT EXISTS third_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE third_db;

-- 创建审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：CREATE-创建，UPDATE-更新，DELETE-删除，SELECT-查询，LOGIN-登录，LOGOUT-登出',
    operation_target VARCHAR(100) NOT NULL COMMENT '操作对象：ACCOUNT-账户，TRANSACTION-交易，USER-用户，SYSTEM-系统',
    target_id BIGINT COMMENT '目标对象ID',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    username VARCHAR(100) NOT NULL COMMENT '操作用户名',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理信息',
    operation_details TEXT COMMENT '操作详情',
    result VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '操作结果：SUCCESS-成功，FAILURE-失败，PARTIAL-部分成功',
    error_message TEXT COMMENT '错误信息',
    execution_time BIGINT COMMENT '执行时间(毫秒)',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- 创建索引
CREATE INDEX idx_audit_logs_operation_type ON audit_logs(operation_type);
CREATE INDEX idx_audit_logs_operation_target ON audit_logs(operation_target);
CREATE INDEX idx_audit_logs_target_id ON audit_logs(target_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_username ON audit_logs(username);
CREATE INDEX idx_audit_logs_result ON audit_logs(result);
CREATE INDEX idx_audit_logs_operation_time ON audit_logs(operation_time);
CREATE INDEX idx_audit_logs_create_time ON audit_logs(create_time);
CREATE INDEX idx_audit_logs_ip_address ON audit_logs(ip_address);

-- 插入测试数据
INSERT INTO audit_logs (operation_type, operation_target, target_id, user_id, username, ip_address, user_agent, operation_details, result, execution_time, operation_time) VALUES
('LOGIN', 'SYSTEM', NULL, 1001, 'admin', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '管理员登录系统', 'SUCCESS', 150, '2024-01-15 08:30:00'),
('CREATE', 'ACCOUNT', 1, 1001, 'admin', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '创建账户：张三储蓄账户', 'SUCCESS', 200, '2024-01-15 09:00:00'),
('CREATE', 'TRANSACTION', 1, 1002, 'zhangsan', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', '存款交易：1000.00元', 'SUCCESS', 180, '2024-01-15 09:30:00'),
('UPDATE', 'ACCOUNT', 1, 1002, 'zhangsan', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', '更新账户余额', 'SUCCESS', 120, '2024-01-15 09:31:00'),
('CREATE', 'TRANSACTION', 2, 1002, 'zhangsan', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', '取款交易：200.00元', 'SUCCESS', 160, '2024-01-15 14:20:00'),
('LOGIN', 'SYSTEM', NULL, 1003, 'lisi', '*************', 'Mozilla/5.0 (Android 10; Mobile; rv:81.0) Gecko/81.0 Firefox/81.0', '用户李四登录', 'SUCCESS', 140, '2024-01-16 10:00:00'),
('CREATE', 'ACCOUNT', 2, 1001, 'admin', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '创建账户：李四支票账户', 'SUCCESS', 190, '2024-01-16 10:10:00'),
('CREATE', 'TRANSACTION', 3, 1003, 'lisi', '*************', 'Mozilla/5.0 (Android 10; Mobile; rv:81.0) Gecko/81.0 Firefox/81.0', '存款交易：500.00元', 'SUCCESS', 170, '2024-01-16 10:15:00'),
('CREATE', 'TRANSACTION', 4, 1003, 'lisi', '*************', 'Mozilla/5.0 (Android 10; Mobile; rv:81.0) Gecko/81.0 Firefox/81.0', '支付交易：150.00元', 'SUCCESS', 200, '2024-01-16 16:45:00'),
('LOGIN', 'SYSTEM', NULL, 1004, 'wangwu', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '用户王五登录', 'SUCCESS', 130, '2024-01-17 11:00:00'),
('CREATE', 'ACCOUNT', 3, 1001, 'admin', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '创建账户：王五信用账户', 'SUCCESS', 210, '2024-01-17 11:20:00'),
('CREATE', 'TRANSACTION', 5, 1004, 'wangwu', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '存款交易：2000.00元', 'SUCCESS', 180, '2024-01-17 11:30:00'),
('CREATE', 'TRANSACTION', 6, 1004, 'wangwu', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '转账交易：800.00元', 'SUCCESS', 220, '2024-01-17 15:20:00'),
('CREATE', 'TRANSACTION', 7, 1005, 'zhaoliu', '192.168.1.104', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '取款交易失败：余额不足', 'FAILURE', 100, '2024-01-18 09:45:00'),
('LOGIN', 'SYSTEM', NULL, 1006, 'qianqi', '10.0.0.50', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '企业用户钱七登录', 'SUCCESS', 160, '2024-01-18 13:00:00'),
('CREATE', 'TRANSACTION', 8, 1006, 'qianqi', '10.0.0.50', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '企业存款：5000.00元', 'SUCCESS', 190, '2024-01-18 13:15:00'),
('CREATE', 'TRANSACTION', 9, 1006, 'qianqi', '10.0.0.50', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '企业支付：1200.00元', 'SUCCESS', 210, '2024-01-18 17:30:00'),
('SELECT', 'ACCOUNT', NULL, 1001, 'admin', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '查询所有账户信息', 'SUCCESS', 80, '2024-01-19 09:00:00'),
('UPDATE', 'ACCOUNT', 9, 1001, 'admin', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '冻结账户：郑十一', 'SUCCESS', 150, '2024-01-19 10:30:00'),
('CREATE', 'TRANSACTION', 15, 1008, 'zhengshiyi', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', '取款交易取消：账户冻结', 'FAILURE', 50, '2024-01-21 09:30:00'),
('LOGIN', 'SYSTEM', NULL, 1007, 'sunba', '*************', 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15', '投资用户孙八登录', 'SUCCESS', 140, '2024-01-19 09:45:00'),
('CREATE', 'TRANSACTION', 10, 1007, 'sunba', '*************', 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15', '理财收益存款：3000.00元', 'SUCCESS', 170, '2024-01-19 10:00:00'),
('SELECT', 'TRANSACTION', NULL, 1001, 'admin', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '查询今日交易统计', 'SUCCESS', 120, '2024-01-22 16:00:00'),
('LOGOUT', 'SYSTEM', NULL, 1001, 'admin', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '管理员退出系统', 'SUCCESS', 30, '2024-01-22 18:00:00'),
('LOGIN', 'SYSTEM', NULL, 1009, 'wushi', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'VIP用户吴十登录', 'SUCCESS', 120, '2024-01-20 11:00:00'),
('CREATE', 'TRANSACTION', 13, 1009, 'wushi', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'VIP大额存款：10000.00元', 'SUCCESS', 250, '2024-01-20 11:20:00'),
('CREATE', 'TRANSACTION', 14, 1009, 'wushi', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'VIP转账服务：2500.00元', 'SUCCESS', 180, '2024-01-20 16:15:00'),
('SELECT', 'ACCOUNT', 8, 1009, 'wushi', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '查询VIP账户详情', 'SUCCESS', 60, '2024-01-20 16:30:00'),
('UPDATE', 'TRANSACTION', 17, 1002, 'zhangsan', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)', '信用卡还款处理中', 'PARTIAL', 300, '2024-01-22 10:30:00'),
('SELECT', 'TRANSACTION', NULL, 1003, 'lisi', '*************', 'Mozilla/5.0 (Android 10; Mobile; rv:81.0) Gecko/81.0 Firefox/81.0', '查询个人交易记录', 'SUCCESS', 90, '2024-01-22 14:00:00');

-- 查看插入结果
SELECT '审计日志统计' as 统计类型, COUNT(*) as 总数 FROM audit_logs;
SELECT '操作类型统计' as 统计类型, operation_type as 类型, COUNT(*) as 数量 FROM audit_logs GROUP BY operation_type;
SELECT '操作对象统计' as 统计类型, operation_target as 对象, COUNT(*) as 数量 FROM audit_logs GROUP BY operation_target;
SELECT '操作结果统计' as 统计类型, result as 结果, COUNT(*) as 数量 FROM audit_logs GROUP BY result;
SELECT '用户操作统计' as 统计类型, username as 用户, COUNT(*) as 操作次数 FROM audit_logs GROUP BY username ORDER BY 操作次数 DESC;
SELECT 'IP地址统计' as 统计类型, ip_address as IP地址, COUNT(*) as 访问次数 FROM audit_logs GROUP BY ip_address ORDER BY 访问次数 DESC;

-- 创建审计统计视图
CREATE VIEW audit_summary AS
SELECT 
    username,
    COUNT(*) as total_operations,
    SUM(CASE WHEN operation_type = 'LOGIN' THEN 1 ELSE 0 END) as login_count,
    SUM(CASE WHEN operation_type = 'CREATE' THEN 1 ELSE 0 END) as create_count,
    SUM(CASE WHEN operation_type = 'UPDATE' THEN 1 ELSE 0 END) as update_count,
    SUM(CASE WHEN operation_type = 'DELETE' THEN 1 ELSE 0 END) as delete_count,
    SUM(CASE WHEN operation_type = 'SELECT' THEN 1 ELSE 0 END) as select_count,
    SUM(CASE WHEN result = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN result = 'FAILURE' THEN 1 ELSE 0 END) as failure_count,
    AVG(execution_time) as avg_execution_time,
    MAX(operation_time) as last_operation_time
FROM audit_logs 
GROUP BY username;

-- 查看审计汇总视图
SELECT '审计汇总视图' as 视图名称;
SELECT * FROM audit_summary ORDER BY total_operations DESC;
