-- 主数据库初始化脚本
-- 数据库: primary_db

CREATE DATABASE IF NOT EXISTS primary_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE primary_db;

-- 创建账户表
CREATE TABLE IF NOT EXISTS accounts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账户ID',
    account_no VARCHAR(50) NOT NULL UNIQUE COMMENT '账户编号',
    account_name VARCHAR(100) NOT NULL COMMENT '账户名称',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    balance DECIMAL(15,2) DEFAULT 0.00 COMMENT '账户余额',
    account_type VARCHAR(20) DEFAULT 'SAVINGS' COMMENT '账户类型：SAVINGS-储蓄，CHECKING-支票，CREDIT-信用',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-活跃，INACTIVE-非活跃，FROZEN-冻结',
    description TEXT COMMENT '描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户表';

-- 创建索引
CREATE INDEX idx_accounts_no ON accounts(account_no);
CREATE INDEX idx_accounts_name ON accounts(account_name);
CREATE INDEX idx_accounts_email ON accounts(email);
CREATE INDEX idx_accounts_type ON accounts(account_type);
CREATE INDEX idx_accounts_status ON accounts(status);
CREATE INDEX idx_accounts_create_time ON accounts(create_time);

-- 插入测试数据
INSERT INTO accounts (account_no, account_name, email, phone, balance, account_type, status, description) VALUES
('ACC001', '张三储蓄账户', '<EMAIL>', '***********', 10000.00, 'SAVINGS', 'ACTIVE', '个人储蓄账户'),
('ACC002', '李四支票账户', '<EMAIL>', '***********', 5000.00, 'CHECKING', 'ACTIVE', '个人支票账户'),
('ACC003', '王五信用账户', '<EMAIL>', '***********', 15000.00, 'CREDIT', 'ACTIVE', '个人信用账户'),
('ACC004', '赵六储蓄账户', '<EMAIL>', '***********', 8000.00, 'SAVINGS', 'INACTIVE', '暂停使用的储蓄账户'),
('ACC005', '钱七企业账户', '<EMAIL>', '***********', 50000.00, 'CHECKING', 'ACTIVE', '企业支票账户'),
('ACC006', '孙八投资账户', '<EMAIL>', '***********', 25000.00, 'SAVINGS', 'ACTIVE', '投资理财账户'),
('ACC007', '周九学生账户', '<EMAIL>', '***********', 1000.00, 'SAVINGS', 'ACTIVE', '学生储蓄账户'),
('ACC008', '吴十VIP账户', '<EMAIL>', '***********', 100000.00, 'CREDIT', 'ACTIVE', 'VIP信用账户'),
('ACC009', '郑十一冻结账户', '<EMAIL>', '***********', 3000.00, 'SAVINGS', 'FROZEN', '风险冻结账户'),
('ACC010', '王十二联名账户', '<EMAIL>', '***********', 20000.00, 'CHECKING', 'ACTIVE', '夫妻联名账户');

-- 查看插入结果
SELECT '账户统计' as 统计类型, COUNT(*) as 总数 FROM accounts;
SELECT '账户类型统计' as 统计类型, account_type as 类型, COUNT(*) as 数量 FROM accounts GROUP BY account_type;
SELECT '账户状态统计' as 统计类型, status as 状态, COUNT(*) as 数量 FROM accounts GROUP BY status;
SELECT '账户余额统计' as 统计类型, SUM(balance) as 总余额, AVG(balance) as 平均余额 FROM accounts WHERE status = 'ACTIVE';
