spring:
  application:
    name: mybatis-official-datasource

  # 主数据源配置（Spring Boot默认数据源）
  datasource:
    url: *************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # HikariCP连接池配置
    hikari:
      pool-name: PrimaryHikariPool
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1

# 应用自定义数据源配置
app:
  # 第二个数据源配置
  datasource:
    url: ***************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    # HikariCP连接池特定配置
    configuration:
      pool-name: SecondaryHikariPool
      maximum-pool-size: 15
      minimum-idle: 3
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      # HikariCP高级配置
      leak-detection-threshold: 60000
      connection-init-sql: SELECT 1
      validation-timeout: 5000
      max-lifetime: 1800000
      keepalive-time: 0
      allow-pool-suspension: false

  # 第三个数据源配置（可选）
  third-datasource:
    url: ***********************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    configuration:
      pool-name: ThirdHikariPool
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# MyBatis配置
mybatis:
  # 映射文件位置
  mapper-locations: 
    - classpath:mapper/primary/*.xml
    - classpath:mapper/secondary/*.xml
    - classpath:mapper/third/*.xml
  # 类型别名包
  type-aliases-package: com.example.domain
  # MyBatis配置
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    cache-enabled: true
    use-generated-keys: true
    # 开启延迟加载
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
    # 开启二级缓存
    cache-enabled: true

# 服务器配置
server:
  port: 8086

# 日志配置
logging:
  level:
    com.example: debug
    org.apache.ibatis: debug
    org.springframework.jdbc: debug
    com.zaxxer.hikari: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,beans,configprops
  endpoint:
    health:
      show-details: always
    configprops:
      show-values: always

# 数据源监控配置
spring.datasource.hikari.register-mbeans: true
