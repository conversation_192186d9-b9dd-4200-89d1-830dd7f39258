<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.secondary.mapper.TransactionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.secondary.domain.Transaction">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="transaction_no" property="transactionNo" jdbcType="VARCHAR"/>
        <result column="account_id" property="accountId" jdbcType="BIGINT"/>
        <result column="account_no" property="accountNo" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="transaction_type" property="transactionType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="reference_no" property="referenceNo" jdbcType="VARCHAR"/>
        <result column="transaction_time" property="transactionTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, transaction_no, account_id, account_no, amount, transaction_type, status, description, 
        reference_no, transaction_time, create_time, update_time
    </sql>

    <!-- 根据ID查询交易 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据交易编号查询交易 -->
    <select id="selectByTransactionNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE transaction_no = #{transactionNo,jdbcType=VARCHAR}
    </select>

    <!-- 查询所有交易 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        ORDER BY transaction_time DESC
    </select>

    <!-- 根据账户ID查询交易 -->
    <select id="selectByAccountId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE account_id = #{accountId,jdbcType=BIGINT}
        ORDER BY transaction_time DESC
    </select>

    <!-- 根据账户编号查询交易 -->
    <select id="selectByAccountNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE account_no = #{accountNo,jdbcType=VARCHAR}
        ORDER BY transaction_time DESC
    </select>

    <!-- 根据交易类型查询交易 -->
    <select id="selectByTransactionType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE transaction_type = #{transactionType,jdbcType=VARCHAR}
        ORDER BY transaction_time DESC
    </select>

    <!-- 根据状态查询交易 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE status = #{status,jdbcType=VARCHAR}
        ORDER BY transaction_time DESC
    </select>

    <!-- 根据金额范围查询交易 -->
    <select id="selectByAmountRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE 1=1
        <if test="minAmount != null">
            AND amount &gt;= #{minAmount,jdbcType=DECIMAL}
        </if>
        <if test="maxAmount != null">
            AND amount &lt;= #{maxAmount,jdbcType=DECIMAL}
        </if>
        ORDER BY amount DESC
    </select>

    <!-- 根据时间范围查询交易 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE 1=1
        <if test="startTime != null">
            AND transaction_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND transaction_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY transaction_time DESC
    </select>

    <!-- 分页查询交易 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        ORDER BY transaction_time DESC
        LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <!-- 统计交易总数 -->
    <select id="countAll" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM transactions
    </select>

    <!-- 根据状态统计交易数 -->
    <select id="countByStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM transactions WHERE status = #{status,jdbcType=VARCHAR}
    </select>

    <!-- 根据交易类型统计交易数 -->
    <select id="countByTransactionType" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM transactions WHERE transaction_type = #{transactionType,jdbcType=VARCHAR}
    </select>

    <!-- 根据账户ID统计交易数 -->
    <select id="countByAccountId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM transactions WHERE account_id = #{accountId,jdbcType=BIGINT}
    </select>

    <!-- 插入交易 -->
    <insert id="insert" parameterType="com.example.secondary.domain.Transaction" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO transactions (transaction_no, account_id, account_no, amount, transaction_type, status, 
                                 description, reference_no, transaction_time, create_time, update_time)
        VALUES (#{transactionNo,jdbcType=VARCHAR}, #{accountId,jdbcType=BIGINT}, #{accountNo,jdbcType=VARCHAR}, 
                #{amount,jdbcType=DECIMAL}, #{transactionType,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
                #{description,jdbcType=VARCHAR}, #{referenceNo,jdbcType=VARCHAR}, #{transactionTime,jdbcType=TIMESTAMP}, 
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入交易 -->
    <insert id="insertSelective" parameterType="com.example.secondary.domain.Transaction" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO transactions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="transactionNo != null">transaction_no,</if>
            <if test="accountId != null">account_id,</if>
            <if test="accountNo != null">account_no,</if>
            <if test="amount != null">amount,</if>
            <if test="transactionType != null">transaction_type,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="referenceNo != null">reference_no,</if>
            <if test="transactionTime != null">transaction_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="transactionNo != null">#{transactionNo,jdbcType=VARCHAR},</if>
            <if test="accountId != null">#{accountId,jdbcType=BIGINT},</if>
            <if test="accountNo != null">#{accountNo,jdbcType=VARCHAR},</if>
            <if test="amount != null">#{amount,jdbcType=DECIMAL},</if>
            <if test="transactionType != null">#{transactionType,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="referenceNo != null">#{referenceNo,jdbcType=VARCHAR},</if>
            <if test="transactionTime != null">#{transactionTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 根据ID更新交易 -->
    <update id="updateById" parameterType="com.example.secondary.domain.Transaction">
        UPDATE transactions
        SET transaction_no = #{transactionNo,jdbcType=VARCHAR},
            account_id = #{accountId,jdbcType=BIGINT},
            account_no = #{accountNo,jdbcType=VARCHAR},
            amount = #{amount,jdbcType=DECIMAL},
            transaction_type = #{transactionType,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            reference_no = #{referenceNo,jdbcType=VARCHAR},
            transaction_time = #{transactionTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新交易 -->
    <update id="updateByIdSelective" parameterType="com.example.secondary.domain.Transaction">
        UPDATE transactions
        <set>
            <if test="transactionNo != null">transaction_no = #{transactionNo,jdbcType=VARCHAR},</if>
            <if test="accountId != null">account_id = #{accountId,jdbcType=BIGINT},</if>
            <if test="accountNo != null">account_no = #{accountNo,jdbcType=VARCHAR},</if>
            <if test="amount != null">amount = #{amount,jdbcType=DECIMAL},</if>
            <if test="transactionType != null">transaction_type = #{transactionType,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=VARCHAR},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="referenceNo != null">reference_no = #{referenceNo,jdbcType=VARCHAR},</if>
            <if test="transactionTime != null">transaction_time = #{transactionTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 更新交易状态 -->
    <update id="updateStatus">
        UPDATE transactions
        SET status = #{status,jdbcType=VARCHAR}, update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除交易 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM transactions WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 批量插入交易 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO transactions (transaction_no, account_id, account_no, amount, transaction_type, status, 
                                 description, reference_no, transaction_time, create_time, update_time)
        VALUES
        <foreach collection="list" item="transaction" separator=",">
            (#{transaction.transactionNo,jdbcType=VARCHAR}, #{transaction.accountId,jdbcType=BIGINT}, 
             #{transaction.accountNo,jdbcType=VARCHAR}, #{transaction.amount,jdbcType=DECIMAL}, 
             #{transaction.transactionType,jdbcType=VARCHAR}, #{transaction.status,jdbcType=VARCHAR}, 
             #{transaction.description,jdbcType=VARCHAR}, #{transaction.referenceNo,jdbcType=VARCHAR}, 
             #{transaction.transactionTime,jdbcType=TIMESTAMP}, #{transaction.createTime,jdbcType=TIMESTAMP}, 
             #{transaction.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <!-- 批量删除交易 -->
    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM transactions WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>

    <!-- 根据交易编号检查是否存在 -->
    <select id="existsByTransactionNo" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM transactions WHERE transaction_no = #{transactionNo,jdbcType=VARCHAR}
    </select>

    <!-- 获取交易金额总和 -->
    <select id="getTotalAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE status = 'COMPLETED'
    </select>

    <!-- 根据交易类型获取金额总和 -->
    <select id="getTotalAmountByType" parameterType="java.lang.String" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0) FROM transactions 
        WHERE transaction_type = #{transactionType,jdbcType=VARCHAR} AND status = 'COMPLETED'
    </select>

    <!-- 根据账户ID获取交易金额总和 -->
    <select id="getTotalAmountByAccountId" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0) FROM transactions 
        WHERE account_id = #{accountId,jdbcType=BIGINT} AND status = 'COMPLETED'
    </select>

    <!-- 获取今日交易统计 -->
    <select id="getTodayTransactions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE DATE(transaction_time) = CURDATE()
        ORDER BY transaction_time DESC
    </select>

    <!-- 获取本月交易统计 -->
    <select id="getMonthlyTransactions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM transactions
        WHERE YEAR(transaction_time) = YEAR(CURDATE()) 
        AND MONTH(transaction_time) = MONTH(CURDATE())
        ORDER BY transaction_time DESC
    </select>

</mapper>
