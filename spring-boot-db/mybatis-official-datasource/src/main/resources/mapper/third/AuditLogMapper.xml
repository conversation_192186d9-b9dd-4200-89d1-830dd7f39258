<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.third.mapper.AuditLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.third.domain.AuditLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="operation_type" property="operationType" jdbcType="VARCHAR"/>
        <result column="operation_target" property="operationTarget" jdbcType="VARCHAR"/>
        <result column="target_id" property="targetId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="operation_details" property="operationDetails" jdbcType="LONGVARCHAR"/>
        <result column="result" property="result" jdbcType="VARCHAR"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="execution_time" property="executionTime" jdbcType="BIGINT"/>
        <result column="operation_time" property="operationTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, operation_type, operation_target, target_id, user_id, username, ip_address, user_agent, 
        operation_details, result, error_message, execution_time, operation_time, create_time
    </sql>

    <!-- 根据ID查询审计日志 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询所有审计日志 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        ORDER BY operation_time DESC
    </select>

    <!-- 根据操作类型查询审计日志 -->
    <select id="selectByOperationType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE operation_type = #{operationType,jdbcType=VARCHAR}
        ORDER BY operation_time DESC
    </select>

    <!-- 根据操作对象查询审计日志 -->
    <select id="selectByOperationTarget" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE operation_target = #{operationTarget,jdbcType=VARCHAR}
        ORDER BY operation_time DESC
    </select>

    <!-- 根据用户ID查询审计日志 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE user_id = #{userId,jdbcType=BIGINT}
        ORDER BY operation_time DESC
    </select>

    <!-- 根据用户名查询审计日志 -->
    <select id="selectByUsername" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE username = #{username,jdbcType=VARCHAR}
        ORDER BY operation_time DESC
    </select>

    <!-- 根据操作结果查询审计日志 -->
    <select id="selectByResult" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE result = #{result,jdbcType=VARCHAR}
        ORDER BY operation_time DESC
    </select>

    <!-- 根据时间范围查询审计日志 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE 1=1
        <if test="startTime != null">
            AND operation_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND operation_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY operation_time DESC
    </select>

    <!-- 根据IP地址查询审计日志 -->
    <select id="selectByIpAddress" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE ip_address = #{ipAddress,jdbcType=VARCHAR}
        ORDER BY operation_time DESC
    </select>

    <!-- 分页查询审计日志 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        ORDER BY operation_time DESC
        LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <!-- 统计审计日志总数 -->
    <select id="countAll" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM audit_logs
    </select>

    <!-- 根据操作类型统计日志数 -->
    <select id="countByOperationType" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM audit_logs WHERE operation_type = #{operationType,jdbcType=VARCHAR}
    </select>

    <!-- 根据操作结果统计日志数 -->
    <select id="countByResult" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM audit_logs WHERE result = #{result,jdbcType=VARCHAR}
    </select>

    <!-- 根据用户ID统计日志数 -->
    <select id="countByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM audit_logs WHERE user_id = #{userId,jdbcType=BIGINT}
    </select>

    <!-- 插入审计日志 -->
    <insert id="insert" parameterType="com.example.third.domain.AuditLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO audit_logs (operation_type, operation_target, target_id, user_id, username, ip_address, 
                               user_agent, operation_details, result, error_message, execution_time, 
                               operation_time, create_time)
        VALUES (#{operationType,jdbcType=VARCHAR}, #{operationTarget,jdbcType=VARCHAR}, #{targetId,jdbcType=BIGINT}, 
                #{userId,jdbcType=BIGINT}, #{username,jdbcType=VARCHAR}, #{ipAddress,jdbcType=VARCHAR}, 
                #{userAgent,jdbcType=VARCHAR}, #{operationDetails,jdbcType=LONGVARCHAR}, #{result,jdbcType=VARCHAR}, 
                #{errorMessage,jdbcType=LONGVARCHAR}, #{executionTime,jdbcType=BIGINT}, 
                #{operationTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入审计日志 -->
    <insert id="insertSelective" parameterType="com.example.third.domain.AuditLog" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO audit_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationType != null">operation_type,</if>
            <if test="operationTarget != null">operation_target,</if>
            <if test="targetId != null">target_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="username != null">username,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="operationDetails != null">operation_details,</if>
            <if test="result != null">result,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="executionTime != null">execution_time,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="operationType != null">#{operationType,jdbcType=VARCHAR},</if>
            <if test="operationTarget != null">#{operationTarget,jdbcType=VARCHAR},</if>
            <if test="targetId != null">#{targetId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="ipAddress != null">#{ipAddress,jdbcType=VARCHAR},</if>
            <if test="userAgent != null">#{userAgent,jdbcType=VARCHAR},</if>
            <if test="operationDetails != null">#{operationDetails,jdbcType=LONGVARCHAR},</if>
            <if test="result != null">#{result,jdbcType=VARCHAR},</if>
            <if test="errorMessage != null">#{errorMessage,jdbcType=LONGVARCHAR},</if>
            <if test="executionTime != null">#{executionTime,jdbcType=BIGINT},</if>
            <if test="operationTime != null">#{operationTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 根据ID更新审计日志 -->
    <update id="updateById" parameterType="com.example.third.domain.AuditLog">
        UPDATE audit_logs
        SET operation_type = #{operationType,jdbcType=VARCHAR},
            operation_target = #{operationTarget,jdbcType=VARCHAR},
            target_id = #{targetId,jdbcType=BIGINT},
            user_id = #{userId,jdbcType=BIGINT},
            username = #{username,jdbcType=VARCHAR},
            ip_address = #{ipAddress,jdbcType=VARCHAR},
            user_agent = #{userAgent,jdbcType=VARCHAR},
            operation_details = #{operationDetails,jdbcType=LONGVARCHAR},
            result = #{result,jdbcType=VARCHAR},
            error_message = #{errorMessage,jdbcType=LONGVARCHAR},
            execution_time = #{executionTime,jdbcType=BIGINT},
            operation_time = #{operationTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新审计日志 -->
    <update id="updateByIdSelective" parameterType="com.example.third.domain.AuditLog">
        UPDATE audit_logs
        <set>
            <if test="operationType != null">operation_type = #{operationType,jdbcType=VARCHAR},</if>
            <if test="operationTarget != null">operation_target = #{operationTarget,jdbcType=VARCHAR},</if>
            <if test="targetId != null">target_id = #{targetId,jdbcType=BIGINT},</if>
            <if test="userId != null">user_id = #{userId,jdbcType=BIGINT},</if>
            <if test="username != null">username = #{username,jdbcType=VARCHAR},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress,jdbcType=VARCHAR},</if>
            <if test="userAgent != null">user_agent = #{userAgent,jdbcType=VARCHAR},</if>
            <if test="operationDetails != null">operation_details = #{operationDetails,jdbcType=LONGVARCHAR},</if>
            <if test="result != null">result = #{result,jdbcType=VARCHAR},</if>
            <if test="errorMessage != null">error_message = #{errorMessage,jdbcType=LONGVARCHAR},</if>
            <if test="executionTime != null">execution_time = #{executionTime,jdbcType=BIGINT},</if>
            <if test="operationTime != null">operation_time = #{operationTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除审计日志 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM audit_logs WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 批量插入审计日志 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO audit_logs (operation_type, operation_target, target_id, user_id, username, ip_address, 
                               user_agent, operation_details, result, error_message, execution_time, 
                               operation_time, create_time)
        VALUES
        <foreach collection="list" item="auditLog" separator=",">
            (#{auditLog.operationType,jdbcType=VARCHAR}, #{auditLog.operationTarget,jdbcType=VARCHAR}, 
             #{auditLog.targetId,jdbcType=BIGINT}, #{auditLog.userId,jdbcType=BIGINT}, 
             #{auditLog.username,jdbcType=VARCHAR}, #{auditLog.ipAddress,jdbcType=VARCHAR}, 
             #{auditLog.userAgent,jdbcType=VARCHAR}, #{auditLog.operationDetails,jdbcType=LONGVARCHAR}, 
             #{auditLog.result,jdbcType=VARCHAR}, #{auditLog.errorMessage,jdbcType=LONGVARCHAR}, 
             #{auditLog.executionTime,jdbcType=BIGINT}, #{auditLog.operationTime,jdbcType=TIMESTAMP}, 
             #{auditLog.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <!-- 批量删除审计日志 -->
    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM audit_logs WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>

    <!-- 删除指定时间之前的日志 -->
    <delete id="deleteByTimeBefore" parameterType="java.time.LocalDateTime">
        DELETE FROM audit_logs WHERE operation_time &lt; #{time,jdbcType=TIMESTAMP}
    </delete>

    <!-- 获取今日操作统计 -->
    <select id="getTodayLogs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE DATE(operation_time) = CURDATE()
        ORDER BY operation_time DESC
    </select>

    <!-- 获取失败操作日志 -->
    <select id="getFailedOperations" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE result = 'FAILURE'
        ORDER BY operation_time DESC
    </select>

    <!-- 获取用户操作统计 -->
    <select id="getUserOperationStats" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE user_id = #{userId,jdbcType=BIGINT}
        <if test="startTime != null">
            AND operation_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND operation_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY operation_time DESC
    </select>

    <!-- 获取操作类型统计 -->
    <select id="getOperationTypeStats" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM audit_logs
        WHERE 1=1
        <if test="startTime != null">
            AND operation_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND operation_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY operation_type, operation_time DESC
    </select>

</mapper>
