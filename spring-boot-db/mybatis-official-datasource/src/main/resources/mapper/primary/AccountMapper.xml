<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.primary.mapper.AccountMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.primary.domain.Account">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="account_no" property="accountNo" jdbcType="VARCHAR"/>
        <result column="account_name" property="accountName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="balance" property="balance" jdbcType="DECIMAL"/>
        <result column="account_type" property="accountType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, account_no, account_name, email, phone, balance, account_type, status, description, create_time, update_time
    </sql>

    <!-- 根据ID查询账户 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM accounts
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据账户编号查询账户 -->
    <select id="selectByAccountNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM accounts
        WHERE account_no = #{accountNo,jdbcType=VARCHAR}
    </select>

    <!-- 查询所有账户 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM accounts
        ORDER BY create_time DESC
    </select>

    <!-- 根据账户类型查询账户 -->
    <select id="selectByAccountType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM accounts
        WHERE account_type = #{accountType,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态查询账户 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM accounts
        WHERE status = #{status,jdbcType=VARCHAR}
        ORDER BY create_time DESC
    </select>

    <!-- 根据余额范围查询账户 -->
    <select id="selectByBalanceRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM accounts
        WHERE 1=1
        <if test="minBalance != null">
            AND balance >= #{minBalance,jdbcType=DECIMAL}
        </if>
        <if test="maxBalance != null">
            AND balance <= #{maxBalance,jdbcType=DECIMAL}
        </if>
        ORDER BY balance DESC
    </select>

    <!-- 分页查询账户 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM accounts
        ORDER BY create_time DESC
        LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>

    <!-- 统计账户总数 -->
    <select id="countAll" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM accounts
    </select>

    <!-- 根据状态统计账户数 -->
    <select id="countByStatus" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM accounts WHERE status = #{status,jdbcType=VARCHAR}
    </select>

    <!-- 根据账户类型统计账户数 -->
    <select id="countByAccountType" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM accounts WHERE account_type = #{accountType,jdbcType=VARCHAR}
    </select>

    <!-- 插入账户 -->
    <insert id="insert" parameterType="com.example.primary.domain.Account" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO accounts (account_no, account_name, email, phone, balance, account_type, status, description, create_time, update_time)
        VALUES (#{accountNo,jdbcType=VARCHAR}, #{accountName,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
                #{phone,jdbcType=VARCHAR}, #{balance,jdbcType=DECIMAL}, #{accountType,jdbcType=VARCHAR}, 
                #{status,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入账户 -->
    <insert id="insertSelective" parameterType="com.example.primary.domain.Account" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO accounts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountNo != null">account_no,</if>
            <if test="accountName != null">account_name,</if>
            <if test="email != null">email,</if>
            <if test="phone != null">phone,</if>
            <if test="balance != null">balance,</if>
            <if test="accountType != null">account_type,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="accountNo != null">#{accountNo,jdbcType=VARCHAR},</if>
            <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
            <if test="email != null">#{email,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
            <if test="accountType != null">#{accountType,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!-- 根据ID更新账户 -->
    <update id="updateById" parameterType="com.example.primary.domain.Account">
        UPDATE accounts
        SET account_no = #{accountNo,jdbcType=VARCHAR},
            account_name = #{accountName,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            phone = #{phone,jdbcType=VARCHAR},
            balance = #{balance,jdbcType=DECIMAL},
            account_type = #{accountType,jdbcType=VARCHAR},
            status = #{status,jdbcType=VARCHAR},
            description = #{description,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择性更新账户 -->
    <update id="updateByIdSelective" parameterType="com.example.primary.domain.Account">
        UPDATE accounts
        <set>
            <if test="accountNo != null">account_no = #{accountNo,jdbcType=VARCHAR},</if>
            <if test="accountName != null">account_name = #{accountName,jdbcType=VARCHAR},</if>
            <if test="email != null">email = #{email,jdbcType=VARCHAR},</if>
            <if test="phone != null">phone = #{phone,jdbcType=VARCHAR},</if>
            <if test="balance != null">balance = #{balance,jdbcType=DECIMAL},</if>
            <if test="accountType != null">account_type = #{accountType,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=VARCHAR},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 更新账户余额 -->
    <update id="updateBalance">
        UPDATE accounts
        SET balance = #{balance,jdbcType=DECIMAL}, update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据ID删除账户 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM accounts WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 批量插入账户 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO accounts (account_no, account_name, email, phone, balance, account_type, status, description, create_time, update_time)
        VALUES
        <foreach collection="list" item="account" separator=",">
            (#{account.accountNo,jdbcType=VARCHAR}, #{account.accountName,jdbcType=VARCHAR}, #{account.email,jdbcType=VARCHAR}, 
             #{account.phone,jdbcType=VARCHAR}, #{account.balance,jdbcType=DECIMAL}, #{account.accountType,jdbcType=VARCHAR}, 
             #{account.status,jdbcType=VARCHAR}, #{account.description,jdbcType=VARCHAR}, #{account.createTime,jdbcType=TIMESTAMP}, 
             #{account.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <!-- 批量删除账户 -->
    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM accounts WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>

    <!-- 根据账户编号检查是否存在 -->
    <select id="existsByAccountNo" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM accounts WHERE account_no = #{accountNo,jdbcType=VARCHAR}
    </select>

    <!-- 获取账户余额总和 -->
    <select id="getTotalBalance" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(balance), 0) FROM accounts WHERE status = 'ACTIVE'
    </select>

    <!-- 根据账户类型获取余额总和 -->
    <select id="getTotalBalanceByType" parameterType="java.lang.String" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(balance), 0) FROM accounts 
        WHERE account_type = #{accountType,jdbcType=VARCHAR} AND status = 'ACTIVE'
    </select>

</mapper>
