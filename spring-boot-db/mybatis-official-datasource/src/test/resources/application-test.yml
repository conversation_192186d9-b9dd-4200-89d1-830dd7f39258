# 测试环境配置
spring:
  application:
    name: mybatis-official-datasource-test

  # 主数据源配置（使用H2内存数据库进行测试）
  datasource:
    url: jdbc:h2:mem:primary_test_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    # HikariCP连接池配置
    hikari:
      pool-name: PrimaryTestHikariPool
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
    # H2数据库初始化
    schema: classpath:sql/test/primary_test_schema.sql
    data: classpath:sql/test/primary_test_data.sql

# 应用自定义数据源配置
app:
  # 第二个数据源配置（使用H2内存数据库）
  datasource:
    url: jdbc:h2:mem:secondary_test_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    # HikariCP连接池特定配置
    configuration:
      pool-name: SecondaryTestHikariPool
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # 第三个数据源配置（使用H2内存数据库）
  third-datasource:
    url: jdbc:h2:mem:third_test_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    configuration:
      pool-name: ThirdTestHikariPool
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# MyBatis配置
mybatis:
  # 映射文件位置
  mapper-locations: 
    - classpath:mapper/primary/*.xml
    - classpath:mapper/secondary/*.xml
    - classpath:mapper/third/*.xml
  # 类型别名包
  type-aliases-package: com.example.domain
  # MyBatis配置
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    cache-enabled: false  # 测试时关闭缓存
    use-generated-keys: true
    lazy-loading-enabled: false  # 测试时关闭延迟加载
    aggressive-lazy-loading: false

# 服务器配置
server:
  port: 0  # 随机端口

# 日志配置
logging:
  level:
    com.example: debug
    org.apache.ibatis: debug
    org.springframework.jdbc: debug
    org.springframework.test: debug
    org.h2: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"

# H2数据库控制台配置（测试时启用）
spring.h2.console.enabled: true
spring.h2.console.path: /h2-console

# 测试专用配置
spring.jpa.show-sql: true
spring.jpa.hibernate.ddl-auto: none
