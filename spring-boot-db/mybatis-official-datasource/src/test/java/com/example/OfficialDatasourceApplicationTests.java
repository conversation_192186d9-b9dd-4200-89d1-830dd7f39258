package com.example;

import com.example.primary.domain.Account;
import com.example.primary.service.AccountService;
import com.example.secondary.domain.Transaction;
import com.example.secondary.service.TransactionService;
import com.example.third.domain.AuditLog;
import com.example.third.service.AuditLogService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatis官方推荐多数据源应用测试类
 * 测试三个数据源的基本功能
 */
@SpringBootTest
@ActiveProfiles("test")
class OfficialDatasourceApplicationTests {

    @Autowired
    private AccountService accountService;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private AuditLogService auditLogService;

    /**
     * 测试应用上下文加载
     */
    @Test
    void contextLoads() {
        assertNotNull(accountService);
        assertNotNull(transactionService);
        assertNotNull(auditLogService);
    }

    /**
     * 测试主数据源 - 账户服务
     */
    @Test
    @Transactional("primaryTransactionManager")
    void testPrimaryDataSource() {
        // 创建测试账户
        Account account = new Account();
        account.setAccountNo("TEST001");
        account.setAccountName("测试账户");
        account.setEmail("<EMAIL>");
        account.setPhone("***********");
        account.setBalance(new BigDecimal("1000.00"));
        account.setAccountType("SAVINGS");
        account.setStatus("ACTIVE");
        account.setDescription("单元测试账户");

        // 测试创建账户
        Account createdAccount = accountService.createAccount(account);
        assertNotNull(createdAccount);
        assertNotNull(createdAccount.getId());
        assertEquals("TEST001", createdAccount.getAccountNo());
        assertEquals("测试账户", createdAccount.getAccountName());

        // 测试查询账户
        Account foundAccount = accountService.getById(createdAccount.getId());
        assertNotNull(foundAccount);
        assertEquals(createdAccount.getId(), foundAccount.getId());

        // 测试根据账户编号查询
        Account foundByNo = accountService.getByAccountNo("TEST001");
        assertNotNull(foundByNo);
        assertEquals("TEST001", foundByNo.getAccountNo());

        // 测试更新账户余额
        accountService.updateBalance(createdAccount.getId(), new BigDecimal("2000.00"));
        Account updatedAccount = accountService.getById(createdAccount.getId());
        assertEquals(0, new BigDecimal("2000.00").compareTo(updatedAccount.getBalance()));

        // 测试统计功能
        int totalCount = accountService.getTotalCount();
        assertTrue(totalCount > 0);

        List<Account> activeAccounts = accountService.getByStatus("ACTIVE");
        assertFalse(activeAccounts.isEmpty());

        // 测试删除账户
        accountService.updateBalance(createdAccount.getId(), BigDecimal.ZERO);
        accountService.deleteAccount(createdAccount.getId());
        
        Account deletedAccount = accountService.getById(createdAccount.getId());
        assertNull(deletedAccount);
    }

    /**
     * 测试第二数据源 - 交易服务
     */
    @Test
    @Transactional("secondaryTransactionManager")
    void testSecondaryDataSource() {
        // 创建测试交易
        Transaction transaction = new Transaction();
        transaction.setTransactionNo("TXN_TEST001");
        transaction.setAccountId(1L);
        transaction.setAccountNo("ACC001");
        transaction.setAmount(new BigDecimal("500.00"));
        transaction.setTransactionType("DEPOSIT");
        transaction.setStatus("PENDING");
        transaction.setDescription("单元测试交易");
        transaction.setReferenceNo("REF_TEST001");

        // 测试创建交易
        Transaction createdTransaction = transactionService.createTransaction(transaction);
        assertNotNull(createdTransaction);
        assertNotNull(createdTransaction.getId());
        assertEquals("TXN_TEST001", createdTransaction.getTransactionNo());
        assertEquals("DEPOSIT", createdTransaction.getTransactionType());

        // 测试查询交易
        Transaction foundTransaction = transactionService.getById(createdTransaction.getId());
        assertNotNull(foundTransaction);
        assertEquals(createdTransaction.getId(), foundTransaction.getId());

        // 测试根据交易编号查询
        Transaction foundByNo = transactionService.getByTransactionNo("TXN_TEST001");
        assertNotNull(foundByNo);
        assertEquals("TXN_TEST001", foundByNo.getTransactionNo());

        // 测试更新交易状态
        transactionService.updateStatus(createdTransaction.getId(), "COMPLETED");
        Transaction updatedTransaction = transactionService.getById(createdTransaction.getId());
        assertEquals("COMPLETED", updatedTransaction.getStatus());

        // 测试根据账户ID查询交易
        List<Transaction> accountTransactions = transactionService.getByAccountId(1L);
        assertFalse(accountTransactions.isEmpty());

        // 测试根据交易类型查询
        List<Transaction> depositTransactions = transactionService.getByTransactionType("DEPOSIT");
        assertFalse(depositTransactions.isEmpty());

        // 测试统计功能
        int totalCount = transactionService.getTotalCount();
        assertTrue(totalCount > 0);

        BigDecimal totalAmount = transactionService.getTotalAmount();
        assertNotNull(totalAmount);

        // 测试删除交易（先改为失败状态）
        transactionService.updateStatus(createdTransaction.getId(), "FAILED");
        transactionService.deleteTransaction(createdTransaction.getId());
        
        Transaction deletedTransaction = transactionService.getById(createdTransaction.getId());
        assertNull(deletedTransaction);
    }

    /**
     * 测试第三数据源 - 审计日志服务
     */
    @Test
    @Transactional("thirdTransactionManager")
    void testThirdDataSource() {
        // 创建测试审计日志
        AuditLog auditLog = new AuditLog();
        auditLog.setOperationType("CREATE");
        auditLog.setOperationTarget("ACCOUNT");
        auditLog.setTargetId(1L);
        auditLog.setUserId(1001L);
        auditLog.setUsername("testuser");
        auditLog.setIpAddress("*************");
        auditLog.setUserAgent("JUnit Test");
        auditLog.setOperationDetails("单元测试创建账户");
        auditLog.setResult("SUCCESS");
        auditLog.setExecutionTime(100L);

        // 测试创建审计日志
        AuditLog createdLog = auditLogService.createLog(auditLog);
        assertNotNull(createdLog);
        assertNotNull(createdLog.getId());
        assertEquals("CREATE", createdLog.getOperationType());
        assertEquals("ACCOUNT", createdLog.getOperationTarget());

        // 测试查询审计日志
        AuditLog foundLog = auditLogService.getById(createdLog.getId());
        assertNotNull(foundLog);
        assertEquals(createdLog.getId(), foundLog.getId());

        // 测试根据操作类型查询
        List<AuditLog> createLogs = auditLogService.getByOperationType("CREATE");
        assertFalse(createLogs.isEmpty());

        // 测试根据用户ID查询
        List<AuditLog> userLogs = auditLogService.getByUserId(1001L);
        assertFalse(userLogs.isEmpty());

        // 测试根据操作结果查询
        List<AuditLog> successLogs = auditLogService.getByResult("SUCCESS");
        assertFalse(successLogs.isEmpty());

        // 测试便捷方法记录日志
        auditLogService.logOperation("UPDATE", "ACCOUNT", 1L, 1001L, "testuser", 
                                    "*************", "测试更新账户");

        // 测试记录失败日志
        auditLogService.logFailedOperation("DELETE", "ACCOUNT", 1L, 1001L, "testuser", 
                                         "*************", "测试删除账户", "权限不足");

        // 测试统计功能
        int totalCount = auditLogService.getTotalCount();
        assertTrue(totalCount > 0);

        int createCount = auditLogService.getCountByOperationType("CREATE");
        assertTrue(createCount > 0);

        // 测试删除审计日志
        auditLogService.deleteLog(createdLog.getId());
        
        AuditLog deletedLog = auditLogService.getById(createdLog.getId());
        assertNull(deletedLog);
    }

    /**
     * 测试多数据源事务隔离
     */
    @Test
    void testMultiDataSourceTransactionIsolation() {
        // 测试不同数据源的事务是独立的
        
        // 在主数据源创建账户
        Account account = new Account();
        account.setAccountNo("ISOLATION_TEST");
        account.setAccountName("事务隔离测试");
        account.setEmail("<EMAIL>");
        account.setBalance(new BigDecimal("1000.00"));
        
        Account createdAccount = accountService.createAccount(account);
        assertNotNull(createdAccount);

        // 在第二数据源创建交易
        Transaction transaction = new Transaction();
        transaction.setTransactionNo("ISOLATION_TXN");
        transaction.setAccountId(createdAccount.getId());
        transaction.setAccountNo(createdAccount.getAccountNo());
        transaction.setAmount(new BigDecimal("100.00"));
        transaction.setTransactionType("WITHDRAW");
        
        Transaction createdTransaction = transactionService.createTransaction(transaction);
        assertNotNull(createdTransaction);

        // 在第三数据源记录审计日志
        auditLogService.logOperation("CREATE", "TRANSACTION", createdTransaction.getId(), 
                                    1001L, "testuser", "*************", "创建交易记录");

        // 验证数据在各自数据源中存在
        assertNotNull(accountService.getById(createdAccount.getId()));
        assertNotNull(transactionService.getById(createdTransaction.getId()));
        
        List<AuditLog> logs = auditLogService.getByOperationType("CREATE");
        assertFalse(logs.isEmpty());

        // 清理测试数据
        accountService.updateBalance(createdAccount.getId(), BigDecimal.ZERO);
        accountService.deleteAccount(createdAccount.getId());
        
        transactionService.updateStatus(createdTransaction.getId(), "FAILED");
        transactionService.deleteTransaction(createdTransaction.getId());
    }

    /**
     * 测试数据源配置
     */
    @Test
    void testDataSourceConfiguration() {
        // 测试各个服务都能正常工作，说明数据源配置正确
        
        // 测试主数据源
        List<Account> accounts = accountService.getAllAccounts();
        assertNotNull(accounts);

        // 测试第二数据源
        List<Transaction> transactions = transactionService.getAllTransactions();
        assertNotNull(transactions);

        // 测试第三数据源
        List<AuditLog> logs = auditLogService.getAllLogs();
        assertNotNull(logs);

        // 测试统计功能
        assertTrue(accountService.getTotalCount() >= 0);
        assertTrue(transactionService.getTotalCount() >= 0);
        assertTrue(auditLogService.getTotalCount() >= 0);
    }
}
