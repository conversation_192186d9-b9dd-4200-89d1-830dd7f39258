# MyBatis官方推荐多数据源 API 示例

## 项目概述

`mybatis-official-datasource` 项目严格按照Spring Boot官方文档推荐方式实现多数据源配置，使用`DataSourceProperties`和`@ConfigurationProperties`实现类型安全的配置绑定。

## 官方推荐配置方式

### 主数据源配置
```java
@Primary
@Bean(name = "primaryDataSourceProperties")
@ConfigurationProperties("spring.datasource")
public DataSourceProperties primaryDataSourceProperties() {
    return new DataSourceProperties();
}

@Primary
@Bean(name = "primaryDataSource")
@ConfigurationProperties("spring.datasource.hikari")
public HikariDataSource primaryDataSource(DataSourceProperties properties) {
    return properties.initializeDataSourceBuilder()
            .type(HikariDataSource.class)
            .build();
}
```

### 附加数据源配置
```java
@Qualifier("second")
@Bean(name = "secondaryDataSource", defaultCandidate = false)
@ConfigurationProperties("app.datasource.configuration")
public HikariDataSource secondaryDataSource(DataSourceProperties properties) {
    return properties.initializeDataSourceBuilder()
            .type(HikariDataSource.class)
            .build();
}
```

## API 接口示例

### 基础URL
```
http://localhost:8086
```

### 1. 系统概览信息

**请求**:
```bash
curl -X GET http://localhost:8086/api/official-multi-datasource/overview
```

**响应示例**:
```json
{
  "accountStatistics": {
    "totalAccounts": 10,
    "activeAccounts": 8,
    "inactiveAccounts": 2,
    "dataSource": "primary"
  },
  "transactionStatistics": {
    "totalTransactions": 25,
    "pendingTransactions": 3,
    "completedTransactions": 22,
    "dataSource": "secondary"
  },
  "auditStatistics": {
    "totalLogs": 50,
    "successLogs": 48,
    "failureLogs": 2,
    "dataSource": "third"
  },
  "message": "官方推荐多数据源统计信息获取成功",
  "configurationMethod": "DataSourceProperties + @ConfigurationProperties"
}
```

### 2. 最新数据查询

**请求**:
```bash
curl -X GET http://localhost:8086/api/official-multi-datasource/latest
```

**响应示例**:
```json
{
  "latestAccounts": [
    {
      "id": 1,
      "accountNo": "ACC001",
      "accountName": "张三",
      "email": "<EMAIL>",
      "balance": 10000.00,
      "status": "ACTIVE"
    }
  ],
  "latestTransactions": [
    {
      "id": 1,
      "transactionNo": "TXN001",
      "accountId": 1,
      "amount": 500.00,
      "transactionType": "DEPOSIT",
      "status": "COMPLETED"
    }
  ],
  "latestAuditLogs": [
    {
      "id": 1,
      "operationType": "CREATE",
      "operationTarget": "ACCOUNT",
      "userId": 1,
      "username": "admin",
      "result": "SUCCESS"
    }
  ],
  "message": "最新数据获取成功",
  "note": "数据来自三个不同的数据源"
}
```

### 3. 数据源健康检查

**请求**:
```bash
curl -X GET http://localhost:8086/api/official-multi-datasource/health
```

**响应示例**:
```json
{
  "primaryDataSource": {
    "status": "UP",
    "totalRecords": 10,
    "message": "主数据源连接正常",
    "dataSource": "primary",
    "database": "primary_db"
  },
  "secondaryDataSource": {
    "status": "UP",
    "totalRecords": 25,
    "message": "第二数据源连接正常",
    "dataSource": "secondary",
    "database": "secondary_db"
  },
  "thirdDataSource": {
    "status": "UP",
    "totalRecords": 50,
    "message": "第三数据源连接正常",
    "dataSource": "third",
    "database": "third_db"
  }
}
```

### 4. 配置信息查询

**请求**:
```bash
curl -X GET http://localhost:8086/api/official-multi-datasource/configuration-info
```

**响应示例**:
```json
{
  "configurationMethod": {
    "approach": "Spring Boot Official Recommendation",
    "primaryMethod": "DataSourceProperties + @ConfigurationProperties",
    "additionalMethod": "DataSourceProperties + @Qualifier + defaultCandidate=false",
    "benefits": [
      "类型安全的配置绑定",
      "支持HikariCP完整配置选项",
      "遵循Spring Boot自动配置最佳实践",
      "IDE友好的配置提示",
      "易于维护和扩展"
    ]
  },
  "dataSources": {
    "primary": {
      "qualifier": "@Primary",
      "configPrefix": "spring.datasource",
      "database": "primary_db",
      "purpose": "账户管理"
    },
    "secondary": {
      "qualifier": "@Qualifier(\"second\")",
      "configPrefix": "app.datasource",
      "database": "secondary_db",
      "purpose": "交易记录"
    },
    "third": {
      "qualifier": "@Qualifier(\"third\")",
      "configPrefix": "app.third-datasource",
      "database": "third_db",
      "purpose": "审计日志"
    }
  },
  "documentation": "https://docs.spring.io/spring-boot/docs/current/reference/html/data.html#data.sql.datasource.multiple"
}
```

### 5. 跨数据源操作演示

**请求**:
```bash
curl -X GET http://localhost:8086/api/official-multi-datasource/demo-cross-datasource
```

**响应示例**:
```json
{
  "accounts": [
    {
      "id": 1,
      "accountNo": "ACC001",
      "accountName": "张三",
      "balance": 10000.00
    }
  ],
  "transactions": [
    {
      "id": 1,
      "transactionNo": "TXN001",
      "amount": 500.00,
      "transactionType": "DEPOSIT"
    }
  ],
  "auditLog": {
    "id": 51,
    "operationType": "CROSS_DATASOURCE_QUERY",
    "operationTarget": "MULTI_DATASOURCE_DEMO",
    "userId": 1,
    "username": "system",
    "result": "SUCCESS"
  },
  "message": "跨数据源操作演示成功",
  "note": "此操作涉及三个不同的数据源"
}
```

## 账户管理 API (主数据源)

### 查询所有账户
```bash
curl -X GET http://localhost:8086/api/accounts
```

### 创建账户
```bash
curl -X POST http://localhost:8086/api/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "accountNo": "ACC002",
    "accountName": "李四",
    "email": "<EMAIL>",
    "balance": 5000.00,
    "accountType": "SAVINGS"
  }'
```

### 根据ID查询账户
```bash
curl -X GET http://localhost:8086/api/accounts/1
```

### 更新账户
```bash
curl -X PUT http://localhost:8086/api/accounts/1 \
  -H "Content-Type: application/json" \
  -d '{
    "accountName": "张三（已更新）",
    "balance": 15000.00
  }'
```

## 交易管理 API (第二数据源)

### 查询所有交易
```bash
curl -X GET http://localhost:8086/api/transactions
```

### 创建交易
```bash
curl -X POST http://localhost:8086/api/transactions \
  -H "Content-Type: application/json" \
  -d '{
    "transactionNo": "TXN002",
    "accountId": 1,
    "accountNo": "ACC001",
    "amount": 1000.00,
    "transactionType": "WITHDRAW"
  }'
```

### 根据交易编号查询
```bash
curl -X GET http://localhost:8086/api/transactions/by-no/TXN001
```

## 审计日志 API (第三数据源)

### 查询所有审计日志
```bash
curl -X GET http://localhost:8086/api/audit-logs
```

### 创建审计日志
```bash
curl -X POST http://localhost:8086/api/audit-logs \
  -H "Content-Type: application/json" \
  -d '{
    "operationType": "UPDATE",
    "operationTarget": "ACCOUNT",
    "targetId": 1,
    "userId": 1,
    "username": "admin",
    "operationDetails": "更新账户信息"
  }'
```

### 根据操作类型查询
```bash
curl -X GET http://localhost:8086/api/audit-logs/by-type/CREATE
```

## 配置文件示例

### application.yml
```yaml
spring:
  # 主数据源配置
  datasource:
    url: **************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      pool-name: PrimaryHikariPool
      maximum-pool-size: 20

# 附加数据源配置
app:
  datasource:
    url: ****************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    configuration:
      pool-name: SecondaryHikariPool
      maximum-pool-size: 15

  third-datasource:
    url: ************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    configuration:
      pool-name: ThirdHikariPool
      maximum-pool-size: 10
```

## 特性优势

1. **官方认可**: 完全遵循Spring Boot官方文档推荐
2. **类型安全**: 使用DataSourceProperties确保配置正确
3. **功能完整**: 支持HikariCP的所有高级特性
4. **易于维护**: 清晰的配置结构和最佳实践
5. **IDE友好**: 完整的配置提示和自动补全
6. **企业级**: 适合大型企业应用的架构设计

## 监控端点

### 健康检查
```bash
curl -X GET http://localhost:8086/actuator/health
```

### 配置属性
```bash
curl -X GET http://localhost:8086/actuator/configprops
```

### 数据源信息
```bash
curl -X GET http://localhost:8086/actuator/beans | grep -i datasource
```
